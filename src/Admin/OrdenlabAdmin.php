<?php

declare(strict_types=1);

namespace App\Admin;

use App\Entity\Ordenlaboratorio;
use Sonata\AdminBundle\Admin\AbstractAdmin;
use Sonata\AdminBundle\Datagrid\DatagridMapper;
use Sonata\AdminBundle\Datagrid\ListMapper;
use Sonata\AdminBundle\Form\FormMapper;
use Sonata\AdminBundle\Show\ShowMapper;
use Symfony\Component\Form\Extension\Core\Type\ChoiceType;
use Sonata\Form\Type\DateRangePickerType;
use Sonata\DoctrineORMAdminBundle\Filter\DateRangeFilter;
use Sonata\DoctrineORMAdminBundle\Filter\ModelFilter;
use Doctrine\ORM\EntityManagerInterface;
use Sonata\AdminBundle\Datagrid\ProxyQueryInterface;


final class OrdenlabAdmin extends AbstractAdmin
{
    private EntityManagerInterface $entityManager;

    public $orderStages = [
        'Orden creada',
        'Completar flujo',
        'Sin micas',
        'Micas asignadas',
        'Laboratorio Esperando Material',
        'Pendiente',
        'Procesando',
        'Calidad',
        'Terminado',
        'pendiente de graduar',
        'entregado'    ];


    public function setEntityManager(EntityManagerInterface $entityManager): void
    {
        $this->entityManager = $entityManager;
    }

    protected function configureQuery(ProxyQueryInterface $query): ProxyQueryInterface
    {
        $query = parent::configureQuery($query);

        $rootAlias = current($query->getRootAliases());

        $query->andWhere(
            $query->expr()->eq($rootAlias . '.status', ':status')
        );

        $query->setParameter('status', '1');
        $query->addOrderBy($rootAlias . '.creacion', 'DESC');

        return $query;
    }

    protected function configureDatagridFilters(DatagridMapper $filter): void
    {
        $filter
            ->add('flujoexpedienteIdflujoexpediente.creacion', DateRangeFilter::class, array(
                'show_filter' => true,
                'label' => "Fecha de creación del flujo",
                'field_type' => DateRangePickerType::class, [
                    'dp_side_by_side' => true,
                    'dp_use_current' => true,
                    'dp_use_seconds' => false,
                    'dp_collapse' => true,
                    'dp_calendar_weeks' => false,
                    'dp_view_mode' => 'days',
                    'dp_min_view_mode' => 'days',
                ]
            ))
            ->add('creacion', DateRangeFilter::class, array(
                'show_filter' => true,
                'label' => "Fecha de creación de la orden",
                'field_type' => DateRangePickerType::class, [
                    'dp_side_by_side' => true,
                    'dp_use_current' => true,
                    'dp_use_seconds' => false,
                    'dp_collapse' => true,
                    'dp_calendar_weeks' => false,
                    'dp_view_mode' => 'days',
                    'dp_min_view_mode' => 'days',
                ]
            ))
            ->add('actualizacion', DateRangeFilter::class, array(
                'show_filter' => true,
                'label' => "Fecha de actualización de la orden",
                'field_type' => DateRangePickerType::class, [
                    'dp_side_by_side' => true,
                    'dp_use_current' => true,
                    'dp_use_seconds' => false,
                    'dp_collapse' => true,
                    'dp_calendar_weeks' => false,
                    'dp_view_mode' => 'days',
                    'dp_min_view_mode' => 'days',
                ]
            ))
            ->add('flujoexpedienteIdflujoexpediente.ventaIdventa.fechaventa', DateRangeFilter::class, array(
                'show_filter' => true,
                'label' => "Fecha de venta",
                'field_type' => DateRangePickerType::class, [
                    'dp_side_by_side' => true,
                    'dp_use_current' => true,
                    'dp_use_seconds' => false,
                    'dp_collapse' => true,
                    'dp_calendar_weeks' => false,
                    'dp_view_mode' => 'days',
                    'dp_min_view_mode' => 'days',
                ]
            ))
            ->add('flujoexpedienteIdflujoexpediente.ventaIdventa.folio', null, array('label' => 'Folio'))
            ->add('flujoexpedienteIdflujoexpediente.ventaIdventa.getFormattedSaleQuotation', null, array('label' => 'Estado de venta'))
            ->add('flujoexpedienteIdflujoexpediente.sucursalIdsucursal', ModelFilter::class, ['label' => "Sucursal del flujo", 'field_options' => ['expanded' => false, 'multiple' => true]])
            ->add('flujoexpedienteIdflujoexpediente.ventaIdventa.sucursalIdsucursal', ModelFilter::class, ['label' => "Sucursal de la venta", 'field_options' => ['expanded' => false, 'multiple' => true]])
            ->add('flujoexpedienteIdflujoexpediente.ventaIdventa.tipoventaIdtipoventa.nombre', null, array('label' => 'Tipo de venta'))
            ->add('flujoexpedienteIdflujoexpediente.clienteIdcliente.nombre', null, array('label' => 'Nombre'))
            ->add('flujoexpedienteIdflujoexpediente.clienteIdcliente.apellidopaterno', null, array('label' => 'Apellido paterno'))
            ->add('flujoexpedienteIdflujoexpediente.clienteIdcliente.apellidomaterno', null, array('label' => 'Apellido materno'))
            ->add('flujoexpedienteIdflujoexpediente.ventaIdventa.beneficiario', null, array('label' => 'Beneficiario'))
            ->add('flujoexpedienteIdflujoexpediente.clienteIdcliente.numeroempleado', null, array('label' => 'Número de empleado'))
            ->add('flujoexpedienteIdflujoexpediente.clienteIdcliente.empresaclienteIdempresacliente', ModelFilter::class, ['label' => "Empresa", 'field_options' => ['expanded' => false, 'multiple' => true]])
            ->add('flujoexpedienteIdflujoexpediente.ventaIdventa.unidadIdunidad.nombre', null, array('label' => 'Unidad'))
            ->add('flujoexpedienteIdflujoexpediente.usuarioIdusuario.nombre', null, array('label' => 'Optometrista'))
            ->add('tipolentecontacto', null, array('label' => 'Tipo de lente de contacto'))
            ->add('disenolenteIddisenolente.nombre', null, array('label' => 'Diseño'))
            ->add('tratamientoIdtratamiento.nombre', null, array('label' => 'Material'))
        ;
    }

    protected function configureListFields(ListMapper $list): void
    {
        $list
            ->add(ListMapper::NAME_ACTIONS, null, [
                'label' => 'Opciones',
                'actions' => [
                    'changeStages' => ['template' => 'CRUD/list__action_changeStages.html.twig'],
                    'delete' => [],
                    'flujoe' => [
                        'template' => 'dashboard_flujo_expediente/list-action-flujo2.html.twig',
                    ],
                ],
            ])
            ->add('stage', 'choice', array(
                'label' => 'Estado',
                'choices' => [
                    '' => '',
                    '0' => '',
                    '1' => 'Orden creada',
                    '2' => 'Completar flujo',
                    '3' => 'Sin micas',
                    '4' => 'Micas asignada',
                    '5' => 'Laboratorio Esperando Material',
                    '6' => 'Pendiente',
                    '7' => 'Procesando',
                    '8' => 'Calidad',
                    '9' => 'Terminado',
                    '10'=> 'pendiente de graduar',
                    '11'=> 'entregado',
                ]
            ))
           ->add('getFoliosFromStocks', null, [
    'label' => 'Folio(s)',
    'mapped' => false,
    'sortable' => false,
    'virtual_field' => true,
    'template' => 'admin/ordenlab/get_folios_order.html.twig',
])
            ->add('flujoexpedienteIdflujoexpediente.ventaIdventa.getFormattedSaleQuotation', null, ['label' => 'Estado de venta'])
            ->add('flujoexpedienteIdflujoexpediente.sucursalIdsucursal.nombre', null, ['label' => 'Sucursal del flujo'])
            ->add('flujoexpedienteIdflujoexpediente.ventaIdventa.sucursalIdsucursal.nombre', null, ['label' => 'Sucursal de la venta'])
            ->add('flujoexpedienteIdflujoexpediente.creacion', 'date', array('label' => 'Fecha de creación del flujo', 'format' => 'd-m-Y  H:i a', 'timezone' => 'America/Mexico_City'))
            ->add('creacion', 'date', array('label' => 'Fecha de creación de la orden', 'format' => 'd-m-Y  H:i a', 'timezone' => 'America/Mexico_City'))
            ->add('actualizacion', 'date', array('label' => 'Fecha de actualización de la orden', 'format' => 'd-m-Y  H:i a', 'timezone' => 'America/Mexico_City'))
            ->add('flujoexpedienteIdflujoexpediente.ventaIdventa.fechaventa', 'date', array('label' => 'Fecha de venta', 'format' => 'd-m-Y  H:i a', 'timezone' => 'America/Mexico_City'))
            ->add('flujoexpedienteIdflujoexpediente.ventaIdventa.tipoventaIdtipoventa.nombre', null, ['label' => 'Tipo de venta'])
            ->add('flujoexpedienteIdflujoexpediente.clienteIdcliente.nombre', null, ['label' => 'Nombre'])
            ->add('flujoexpedienteIdflujoexpediente.clienteIdcliente.apellidopaterno', null, ['label' => 'Apellido paterno'])
            ->add('flujoexpedienteIdflujoexpediente.clienteIdcliente.apellidomaterno', null, ['label' => 'Apellido materno'])
            ->add('flujoexpedienteIdflujoexpediente.ventaIdventa.beneficiario', null, ['label' => 'Beneficiario'])
            ->add('flujoexpedienteIdflujoexpediente.clienteIdcliente.numeroempleado', null, ['label' => 'Número de empleado'])
            ->add('flujoexpedienteIdflujoexpediente.clienteIdcliente.empresaclienteIdempresacliente.nombre', null, ['label' => 'Empresa'])
            ->add('flujoexpedienteIdflujoexpediente.clienteIdcliente.unidadIdunidad.nombre', null, ['label' => 'Unidad'])
            ->add('flujoexpedienteIdflujoexpediente.usuarioIdusuario.nombre', null, ['label' => 'Optometrista'])
            ->add('tipoorden', 'choice', array(
                'label' => 'Tipo de orden',
                'choices' => [
                    '' => '',
                    '0' => '',
                    '1' => 'Armazón',
                    '2' => 'Lente de contacto',
                ]
            ))
            ->add('esferaod', null, ['label' => 'Esfera OD'])
            ->add('esferaoi', null, ['label' => 'Esfera OI'])
            ->add('cilindrood', null, ['label' => 'Cilindro OD'])
            ->add('cilindrooi', null, ['label' => 'Cilindro OI'])
            ->add('ejeod', null, ['label' => 'Eje OD'])
            ->add('ejeoi', null, ['label' => 'Eje OI'])
            ->add('dip', null, ['label' => 'DIP'])
            ->add('ao', null, ['label' => 'AO'])
            ->add('aco', null, ['label' => 'ACO'])
            ->add('flujoexpedienteIdflujoexpediente.graduacionIdgraduacion.esfodsf', null, array('label'=>'Subjetiva final Esfera OD'))
            ->add('flujoexpedienteIdflujoexpediente.graduacionIdgraduacion.esfoisf', null, array('label'=>'Subjetiva final Esfera OI'))
            ->add('flujoexpedienteIdflujoexpediente.graduacionIdgraduacion.cilodsf', null, array('label'=>'Subjetiva final Cilindro OD'))
            ->add('flujoexpedienteIdflujoexpediente.graduacionIdgraduacion.ciloisf', null, array('label'=>'Subjetiva final Cilindro OI'))
            ->add('flujoexpedienteIdflujoexpediente.graduacionIdgraduacion.ejeodsf', null, array('label'=>'Subjetiva final Eje OD'))
            ->add('flujoexpedienteIdflujoexpediente.graduacionIdgraduacion.ejeoisf', null, array('label'=>'Subjetiva final Eje OI'))
            ->add('flujoexpedienteIdflujoexpediente.graduacionIdgraduacion.avlodsf', null, array('label'=>'Subjetiva final AV lejos OD'))
            ->add('flujoexpedienteIdflujoexpediente.graduacionIdgraduacion.avloisf', null, array('label'=>'Subjetiva final AV lejos OI'))
            ->add('flujoexpedienteIdflujoexpediente.graduacionIdgraduacion.avcsaodsf', null, array('label'=>'Subjetiva final AV cerca s/Add OD'))
            ->add('flujoexpedienteIdflujoexpediente.graduacionIdgraduacion.avcsaoisf', null, array('label'=>'Subjetiva final AV cerca s/Add OI'))
            ->add('flujoexpedienteIdflujoexpediente.graduacionIdgraduacion.avccaodsf', null, array('label'=>'Subjetiva final AV cerca c/Add OD'))
            ->add('flujoexpedienteIdflujoexpediente.graduacionIdgraduacion.avccaoisf', null, array('label'=>'Subjetiva final AV cerca c/Add OI'))
            ->add('tipolentecontacto', null, ['label' => 'Tipo de lente de contacto'])
            ->add('cb', null, ['label' => 'Curva base'])
            ->add('diam', null, ['label' => 'Diámetro'])
            ->add('armazoncliente', 'choice', array(
                'label' => 'Armazón propio del cliente',
                'choices' => [
                    '' => '',
                    '0' => 'No',
                    '1' => 'Si',
                ]
            ))
            ->add('observaciones', null, ['label' => 'Observaciones'])
            ->add('disenolenteIddisenolente.nombre', null, ['label' => 'Diseño'])
            ->add('tratamientoIdtratamiento.nombre', null, ['label' => 'Material'])
            ->add('suggestions', null, ['label' => 'Sugerencias'])
            ->add('flujoexpedienteIdflujoexpediente.ventaIdventa.products', null, [
                'label' => 'Productos de la venta',
                /*                'template' => 'admin/ordenlab/get_products.html.twig',
                                'mapped' => false,
                                'sortable' => false,
                                'virtual_field' => true,
                                'template' => 'admin/ordenlab/get_products.html.twig',*/
            ])
            ->add('products', null, [
                'label' => 'Productos de la orden',
/*                'mapped' => false,
                'sortable' => false,
                'virtual_field' => true,
                'template' => 'admin/ordenlab/get_products_order.html.twig',*/
            ])
        ;
    }

    protected function configureExportFields(): array
    {
        $exportFields = array(
            // Información básica de la venta
            'Folio(s)' => 'getFoliosFromStocksForExport',
            'Estado de venta' => 'flujoexpedienteIdflujoexpediente.ventaIdventa.getFormattedSaleQuotation',
            'Sucursal del flujo' => 'flujoexpedienteIdflujoexpediente.sucursalIdsucursal.nombre',
            'Sucursal de la venta' => 'flujoexpedienteIdflujoexpediente.ventaIdventa.sucursalIdsucursal.nombre',
            'Fecha de creación del flujo' => 'flujoexpedienteIdflujoexpediente.creacion',
            'Fecha de creación de la orden' => 'creacion',
            'Fecha de actualización de la orden' => 'actualizacion',
            'Fecha de venta' => 'flujoexpedienteIdflujoexpediente.ventaIdventa.fecha',
            'Tipo de venta' => 'flujoexpedienteIdflujoexpediente.ventaIdventa.tipoventaIdtipoventa.nombre',

            // Información del cliente
            'Nombre' => 'flujoexpedienteIdflujoexpediente.clienteIdcliente.nombre',
            'Apellido paterno' => 'flujoexpedienteIdflujoexpediente.clienteIdcliente.apellidopaterno',
            'Apellido materno' => 'flujoexpedienteIdflujoexpediente.clienteIdcliente.apellidomaterno',
            'Beneficiario' => 'flujoexpedienteIdflujoexpediente.ventaIdventa.beneficiario',
            'Número de empleado' => 'flujoexpedienteIdflujoexpediente.clienteIdcliente.numeroempleado',
            'Empresa' => 'flujoexpedienteIdflujoexpediente.clienteIdcliente.empresaclienteIdempresacliente.nombre',
            'Unidad' => 'flujoexpedienteIdflujoexpediente.clienteIdcliente.unidadIdunidad.nombre',
            'Optometrista' => 'flujoexpedienteIdflujoexpediente.usuarioIdusuario.nombre',

            // Información de la orden de laboratorio
            'Tipo de orden' => 'getTipoOrdenFormateado',
            'Esfera OD' => 'esferaod',
            'Esfera OI' => 'esferaoi',
            'Cilindro OD' => 'cilindrood',
            'Cilindro OI' => 'cilindrooi',
            'Eje OD' => 'ejeod',
            'Eje OI' => 'ejeoi',
            'DIP' => 'dip',
            'AO' => 'ao',
            'ACO' => 'aco',
            // Datos de graduación subjetiva final (desde flujo expediente)
            'Subjetiva final Esfera OD' => 'flujoexpedienteIdflujoexpediente.graduacionIdgraduacion.esfodsf',
            'Subjetiva final Esfera OI' => 'flujoexpedienteIdflujoexpediente.graduacionIdgraduacion.esfoisf',
            'Subjetiva final Cilindro OD' => 'flujoexpedienteIdflujoexpediente.graduacionIdgraduacion.cilodsf',
            'Subjetiva final Cilindro OI' => 'flujoexpedienteIdflujoexpediente.graduacionIdgraduacion.ciloisf',
            'Subjetiva final Eje OD' => 'flujoexpedienteIdflujoexpediente.graduacionIdgraduacion.ejeodsf',
            'Subjetiva final Eje OI' => 'flujoexpedienteIdflujoexpediente.graduacionIdgraduacion.ejeoisf',
            'Subjetiva final AV lejos OD' => 'flujoexpedienteIdflujoexpediente.graduacionIdgraduacion.avlodsf',
            'Subjetiva final AV lejos OI' => 'flujoexpedienteIdflujoexpediente.graduacionIdgraduacion.avloisf',
            'Subjetiva final AV cerca s/Add OD' => 'flujoexpedienteIdflujoexpediente.graduacionIdgraduacion.avcsaodsf',
            'Subjetiva final AV cerca s/Add OI' => 'flujoexpedienteIdflujoexpediente.graduacionIdgraduacion.avcsaoisf',
            'Subjetiva final AV cerca c/Add OD' => 'flujoexpedienteIdflujoexpediente.graduacionIdgraduacion.avccaodsf',
            'Subjetiva final AV cerca c/Add OI' => 'flujoexpedienteIdflujoexpediente.graduacionIdgraduacion.avccaoisf',

            // Información específica de lentes de contacto
            'Tipo de lente de contacto' => 'getTipoLenteContactoFormateado',
            'Curva base' => 'cb',
            'Diámetro' => 'diam',

            // Información adicional
            'Armazón propio del cliente' => 'armazoncliente',
            'Observaciones' => 'observaciones',
            'Diseño' => 'disenolenteIddisenolente.nombre',
            'Material' => 'materialIdmaterial.nombre',
            'Tratamiento' => 'tratamientoIdtratamiento.nombre',
            'Sugerencias' => 'suggestions',

            // Productos relacionados
            'Productos de la venta' => 'getProductosVentaForExport',
            'Productos de la orden' => 'products',
        );

        return $exportFields;
    }

    public function getExportFormats(): array
    {
        return ['csv', 'xlsx'];
    }

    /**
     * Obtiene los folios de venta relacionados con un flujo de expediente
     */
    public function getVenta($flujoexpediente): array
    {
        if (!$flujoexpediente) {
            return [];
        }

        try {
            // Verificar si es un objeto o un ID
            $flujoexpedienteId = is_object($flujoexpediente)
                ? $flujoexpediente->getIdflujoexpediente()
                : $flujoexpediente;

            // Consulta SQL real para obtener folios de la base de datos
            $sql = "SELECT v.folio, v.idventa
                    FROM flujoExpedienteVenta fev
                    INNER JOIN flujoExpediente fe ON fev.flujoExpediente_idflujoExpediente = fe.idflujoExpediente
                    INNER JOIN venta v ON fev.venta_idventa = v.idventa
                    WHERE fe.idflujoExpediente = ? AND v.status = '1'";

            // Log para debug
            error_log("getVenta - Ejecutando consulta para flujo: " . $flujoexpedienteId);

            $stmt = $this->entityManager->getConnection()->prepare($sql);
            $result = $stmt->executeQuery([$flujoexpedienteId])->fetchAllAssociative();

            // Log del resultado
            error_log("getVenta - Resultados para flujo " . $flujoexpedienteId . ": " . count($result) . " folios");
            if (!empty($result)) {
                error_log("getVenta - Primer folio: " . $result[0]['folio']);
            }

            return $result;

        } catch (\Exception $e) {
            // En caso de error, devolver array vacío
            return [];
        }
    }
/*
    public function getProducts($idflujoexpediente): array
    {
        $result = $this->entityManager->getRepository(Ordenlaboratorio::class)->getProducts($idflujoexpediente);

        return $result['exito'] ? $result['result'] : [];
    }

    public function getProductsOrder($idordenlaboratorio): array
    {
        $result = $this->entityManager->getRepository(Ordenlaboratorio::class)->getProductsOrder($idordenlaboratorio);

        return $result['exito'] ? $result['result'] : [];
    }*/

     public function getFoliosFromStocks(Ordenlaboratorio $orden): array
{
    try {
        // Método principal: A través de stockventaordenlaboratorio -> stockventa -> venta
        $sql = "SELECT DISTINCT v.folio
                FROM stockVentaOrdenLaboratorio svol
                INNER JOIN stockVenta sv ON svol.stockVenta_idstockVenta = sv.idstockVenta
                INNER JOIN venta v ON sv.venta_idventa = v.idventa
                WHERE svol.ordenLaboratorio_idordenLaboratorio = ?
                AND v.status = '1'
                ORDER BY v.folio";

        $stmt = $this->entityManager->getConnection()->prepare($sql);
        $result = $stmt->executeQuery([$orden->getIdordenlaboratorio()])->fetchAllAssociative();

        if (!empty($result)) {
            return array_column($result, 'folio');
        }

        // Método de respaldo: A través del flujo de expediente (mantener por compatibilidad)
        $flujo = $orden->getFlujoexpedienteIdflujoexpediente();
        if ($flujo && $flujo->getVentaIdventa()) {
            $venta = $flujo->getVentaIdventa();
            if ($venta && $venta->getFolio()) {
                return [$venta->getFolio()];
            }
        }

        return [];

    } catch (\Exception $e) {
        // Log del error para debugging
        error_log("Error en getFoliosFromStocks: " . $e->getMessage());
        return [];
    }
}

    /**
     * Método para obtener folios formateados para exportación
     */
    public function getFoliosFromStocksForExport(Ordenlaboratorio $orden): string
    {
        $folios = $this->getFoliosFromStocks($orden);
        return !empty($folios) ? implode(', ', $folios) : 'Sin folios';
    }

    /**
     * Obtiene los productos de la venta relacionados con la orden
     */
    public function getProductosVentaForExport(Ordenlaboratorio $orden): string
    {
        try {
            $sql = "SELECT DISTINCT CONCAT(p.nombre, ' - ', p.modelo) as producto
                    FROM stockVentaOrdenLaboratorio svol
                    INNER JOIN stockVenta sv ON svol.stockVenta_idstockVenta = sv.idstockVenta
                    INNER JOIN venta v ON sv.venta_idventa = v.idventa
                    INNER JOIN stock s ON sv.stock_idstock = s.idstock
                    INNER JOIN producto p ON s.producto_idproducto = p.idproducto
                    WHERE svol.ordenLaboratorio_idordenLaboratorio = ?
                    AND v.status = '1'";

            $stmt = $this->entityManager->getConnection()->prepare($sql);
            $result = $stmt->executeQuery([$orden->getIdordenlaboratorio()])->fetchAllAssociative();

            if (!empty($result)) {
                return implode(', ', array_column($result, 'producto'));
            }

            return 'Sin productos';

        } catch (\Exception $e) {
            return 'Error al obtener productos';
        }
    }

    /**
     * Obtiene información completa del material y tratamiento
     */
    public function getMaterialTratamientoForExport(Ordenlaboratorio $orden): string
    {
        $material = $orden->getMaterialIdmaterial();
        $tratamiento = $orden->getTratamientoIdtratamiento();

        $info = [];
        if ($material) {
            $info[] = 'Material: ' . $material->getNombre();
        }
        if ($tratamiento) {
            $info[] = 'Tratamiento: ' . $tratamiento->getNombre();
        }

        return !empty($info) ? implode(' | ', $info) : 'Sin especificar';
    }

    /**
     * Obtiene el tipo de lente de contacto formateado
     */
    public function getTipoLenteContactoFormateado(Ordenlaboratorio $orden): string
    {
        $tipo = $orden->getTipolentecontacto();

        switch ($tipo) {
            case '1':
                return 'Blando';
            case '2':
                return 'Rígido';
            default:
                return 'No especificado';
        }
    }

    /**
     * Obtiene el tipo de orden formateado
     */
    public function getTipoOrdenFormateado(Ordenlaboratorio $orden): string
    {
        $tipo = $orden->getTipoorden();

        switch ($tipo) {
            case '1':
                return 'Armazón';
            case '2':
                return 'Lente de contacto';
            default:
                return 'No especificado';
        }
    }

    /**
     * Obtiene toda la información completa de las órdenes de laboratorio
     * con todos los campos solicitados
     */
    public function getInformacionCompletaOrdenes($filtros = []): array
    {
        try {
            $sql = "SELECT DISTINCT
                -- Folios de venta
                GROUP_CONCAT(DISTINCT v.folio ORDER BY v.folio SEPARATOR ', ') as folios,

                -- Estado de venta
                CASE
                    WHEN v.cotizacion = '1' THEN 'Cotización'
                    ELSE 'Venta'
                END as estado_venta,

                -- Sucursales
                suc_flujo.nombre as sucursal_flujo,
                suc_venta.nombre as sucursal_venta,

                -- Fechas
                fe.creacion as fecha_creacion_flujo,
                ol.creacion as fecha_creacion_orden,
                ol.actualizacion as fecha_actualizacion_orden,
                v.fecha as fecha_venta,

                -- Tipo de venta
                tv.nombre as tipo_venta,

                -- Información del cliente
                c.nombre as cliente_nombre,
                c.apellidopaterno as cliente_apellido_paterno,
                c.apellidomaterno as cliente_apellido_materno,
                v.beneficiario as beneficiario,
                c.numeroempleado as numero_empleado,
                ec.nombre as empresa,
                u.nombre as unidad,

                -- Optometrista
                CONCAT(usr.nombre, ' ', usr.apellidopaterno, ' ', usr.apellidomaterno) as optometrista,

                -- Información de la orden de laboratorio
                CASE
                    WHEN ol.tipoorden = '1' THEN 'Armazón'
                    WHEN ol.tipoorden = '2' THEN 'Lente de contacto'
                    ELSE 'No especificado'
                END as tipo_orden,

                ol.esferaod as esfera_od,
                ol.esferaoi as esfera_oi,
                ol.cilindrood as cilindro_od,
                ol.cilindrooi as cilindro_oi,
                ol.ejeod as eje_od,
                ol.ejeoi as eje_oi,
                ol.dip as dip,
                ol.ao as ao,
                ol.aco as aco,

                -- Graduación subjetiva final
                g.esfodsf as subjetiva_final_esfera_od,
                g.esfoisf as subjetiva_final_esfera_oi,
                g.cilodsf as subjetiva_final_cilindro_od,
                g.ciloisf as subjetiva_final_cilindro_oi,
                g.ejeodsf as subjetiva_final_eje_od,
                g.ejeoisf as subjetiva_final_eje_oi,
                g.avlodsf as subjetiva_final_av_lejos_od,
                g.avloisf as subjetiva_final_av_lejos_oi,
                g.avcsaodsf as subjetiva_final_av_cerca_sadd_od,
                g.avcsaoisf as subjetiva_final_av_cerca_sadd_oi,
                g.avccaodsf as subjetiva_final_av_cerca_cadd_od,
                g.avccaoisf as subjetiva_final_av_cerca_cadd_oi,

                -- Información específica de lentes de contacto
                CASE
                    WHEN ol.tipolentecontacto = '1' THEN 'Blando'
                    WHEN ol.tipolentecontacto = '2' THEN 'Rígido'
                    ELSE 'No especificado'
                END as tipo_lente_contacto,
                ol.cb as curva_base,
                ol.diam as diametro,

                -- Información adicional
                ol.armazoncliente as armazon_propio_cliente,
                ol.observaciones as observaciones,
                dl.nombre as diseno,
                m.nombre as material,
                tr.nombre as tratamiento,
                ol.suggestions as sugerencias,

                -- Productos
                GROUP_CONCAT(DISTINCT CONCAT(p.nombre, ' - ', p.modelo) ORDER BY p.nombre SEPARATOR ', ') as productos_venta,
                ol.products as productos_orden

            FROM ordenlaboratorio ol
            INNER JOIN flujoexpediente fe ON ol.flujoexpediente_idflujoexpediente = fe.idflujoexpediente
            INNER JOIN cliente c ON fe.cliente_idcliente = c.idcliente
            LEFT JOIN empresacliente ec ON c.empresacliente_idempresacliente = ec.idempresacliente
            LEFT JOIN unidad u ON c.unidad_idunidad = u.idunidad
            LEFT JOIN usuario usr ON fe.usuario_idusuario = usr.idusuario
            LEFT JOIN sucursal suc_flujo ON fe.sucursal_idsucursal = suc_flujo.idsucursal
            LEFT JOIN venta v ON fe.venta_idventa = v.idventa
            LEFT JOIN sucursal suc_venta ON v.sucursal_idsucursal = suc_venta.idsucursal
            LEFT JOIN tipoventa tv ON v.tipoventa_idtipoventa = tv.idtipoventa
            LEFT JOIN stockventaordenlaboratorio svol ON svol.ordenlaboratorio_idordenlaboratorio = ol.idordenlaboratorio
            LEFT JOIN stockventa sv ON svol.stockventa_idstockventa = sv.idstockventa
            LEFT JOIN stock st ON sv.stock_idstock = st.idstock
            LEFT JOIN producto p ON st.producto_idproducto = p.idproducto
            LEFT JOIN graduacion g ON fe.graduacion_idgraduacion = g.idgraduacion
            LEFT JOIN disenolente dl ON ol.disenolente_iddisenolente = dl.iddisenolente
            LEFT JOIN material m ON ol.material_idmaterial = m.idmaterial
            LEFT JOIN tratamiento tr ON ol.tratamiento_idtratamiento = tr.idtratamiento

            WHERE ol.status = '1'";

            // Agregar filtros si se proporcionan
            if (!empty($filtros['fecha_inicio']) && !empty($filtros['fecha_fin'])) {
                $sql .= " AND ol.creacion BETWEEN :fecha_inicio AND :fecha_fin";
            }

            if (!empty($filtros['sucursal'])) {
                $sql .= " AND suc_flujo.idsucursal = :sucursal";
            }

            $sql .= " GROUP BY ol.idordenlaboratorio
                     ORDER BY ol.creacion DESC";

            $stmt = $this->entityManager->getConnection()->prepare($sql);

            // Establecer parámetros si existen filtros
            if (!empty($filtros['fecha_inicio']) && !empty($filtros['fecha_fin'])) {
                $stmt->bindValue('fecha_inicio', $filtros['fecha_inicio']);
                $stmt->bindValue('fecha_fin', $filtros['fecha_fin']);
            }

            if (!empty($filtros['sucursal'])) {
                $stmt->bindValue('sucursal', $filtros['sucursal']);
            }

            $result = $stmt->executeQuery()->fetchAllAssociative();

            return $result;

        } catch (\Exception $e) {
            error_log("Error en getInformacionCompletaOrdenes: " . $e->getMessage());
            return [];
        }
    }

    /**
     * Método simplificado para obtener información básica de prueba
     */
    public function getInformacionBasicaOrdenes($limite = 10): array
    {
        try {
            $sql = "SELECT
                ol.idordenlaboratorio,
                GROUP_CONCAT(DISTINCT v.folio ORDER BY v.folio SEPARATOR ', ') as folios,
                CASE
                    WHEN v.cotizacion = '1' THEN 'Cotización'
                    ELSE 'Venta'
                END as estado_venta,
                suc_flujo.nombre as sucursal_flujo,
                c.nombre as cliente_nombre,
                c.apellidopaterno as cliente_apellido_paterno,
                ol.esferaod as esfera_od,
                ol.esferaoi as esfera_oi,
                ol.creacion as fecha_creacion_orden

            FROM ordenlaboratorio ol
            INNER JOIN flujoexpediente fe ON ol.flujoexpediente_idflujoexpediente = fe.idflujoexpediente
            INNER JOIN cliente c ON fe.cliente_idcliente = c.idcliente
            LEFT JOIN sucursal suc_flujo ON fe.sucursal_idsucursal = suc_flujo.idsucursal
            LEFT JOIN stockventaordenlaboratorio svol ON svol.ordenlaboratorio_idordenlaboratorio = ol.idordenlaboratorio
            LEFT JOIN stockventa sv ON svol.stockventa_idstockventa = sv.idstockventa
            LEFT JOIN venta v ON sv.venta_idventa = v.idventa

            WHERE ol.status = '1'
            GROUP BY ol.idordenlaboratorio
            ORDER BY ol.creacion DESC
            LIMIT ?";

            $stmt = $this->entityManager->getConnection()->prepare($sql);
            $result = $stmt->executeQuery([$limite])->fetchAllAssociative();

            return $result;

        } catch (\Exception $e) {
            error_log("Error en getInformacionBasicaOrdenes: " . $e->getMessage());
            return [];
        }
    }

}
