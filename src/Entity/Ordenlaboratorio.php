<?php

namespace App\Entity;

use Doctrine\ORM\Mapping as ORM;

/**
 * Ordenlaboratorio
 *
 * @ORM\Table(name="ordenLaboratorio", indexes={@ORM\Index(name="fk_ordenLaboratorio_beneficiario1_idx", columns={"beneficiario_idbeneficiario"}), @ORM\Index(name="fk_ordenLaboratorio_cliente1_idx", columns={"cliente_idcliente"}), @ORM\Index(name="fk_ordenLaboratorio_disenoLente1_idx", columns={"disenoLente_iddisenoLente"}), @ORM\Index(name="fk_ordenLaboratorio_flujoExpediente1_idx", columns={"flujoExpediente_idflujoExpediente"}), @ORM\Index(name="fk_ordenLaboratorio_material1_idx", columns={"material_idmaterial"}), @ORM\Index(name="fk_ordenLaboratorio_proveedor1_idx", columns={"proveedor_idproveedor"}), @ORM\Index(name="fk_ordenLaboratorio_tratamiento1_idx", columns={"tratamiento_idtratamiento"})})
 * @ORM\Entity
 */
class Ordenlaboratorio
{
    /**
     * @var int
     *
     * @ORM\Column(name="idordenLaboratorio", type="integer", nullable=false, options={"unsigned"=true})
     * @ORM\Id
     * @ORM\GeneratedValue(strategy="IDENTITY")
     */
    private $idordenlaboratorio;

    /**
     * @var string|null
     *
     * @ORM\Column(name="rxAnt", type="string", length=45, nullable=true)
     */
    private $rxant;

    /**
     * @var string|null
     *
     * @ORM\Column(name="ojoDerecho", type="string", length=45, nullable=true)
     */
    private $ojoderecho;

    /**
     * @var string|null
     *
     * @ORM\Column(name="ojoIzquierdo", type="string", length=45, nullable=true)
     */
    private $ojoizquierdo;

    /**
     * @var string|null
     *
     * @ORM\Column(name="esferaOD", type="string", length=45, nullable=true)
     */
    private $esferaod;

    /**
     * @var string|null
     *
     * @ORM\Column(name="esferaOI", type="string", length=45, nullable=true)
     */
    private $esferaoi;

    /**
     * @var string|null
     *
     * @ORM\Column(name="cilindroOD", type="string", length=45, nullable=true)
     */
    private $cilindrood;

    /**
     * @var string|null
     *
     * @ORM\Column(name="cilindroOI", type="string", length=45, nullable=true)
     */
    private $cilindrooi;

    /**
     * @var string|null
     *
     * @ORM\Column(name="ejeOD", type="string", length=45, nullable=true)
     */
    private $ejeod;

    /**
     * @var string|null
     *
     * @ORM\Column(name="ejeOI", type="string", length=45, nullable=true)
     */
    private $ejeoi;

    /**
     * @var string|null
     *
     * @ORM\Column(name="AVLejosOD", type="string", length=45, nullable=true)
     */
    private $avlejosod;

    /**
     * @var string|null
     *
     * @ORM\Column(name="AVLejosOI", type="string", length=45, nullable=true)
     */
    private $avlejosoi;

    /**
     * @var string|null
     *
     * @ORM\Column(name="AVCercaSAddOD", type="string", length=45, nullable=true)
     */
    private $avcercasaddod;

    /**
     * @var string|null
     *
     * @ORM\Column(name="AVCercaSAddOI", type="string", length=45, nullable=true)
     */
    private $avcercasaddoi;

    /**
     * @var string|null
     *
     * @ORM\Column(name="AVCercaCAddOD", type="string", length=45, nullable=true)
     */
    private $avcercacaddod;

    /**
     * @var string|null
     *
     * @ORM\Column(name="AVCercaCAddOI", type="string", length=45, nullable=true)
     */
    private $avcercacaddoi;

    /**
     * @var string|null
     *
     * @ORM\Column(name="armazonCliente", type="string", length=45, nullable=true)
     */
    private $armazoncliente;

    /**
     * @var string|null
     *
     * @ORM\Column(name="codigoBarras", type="string", length=45, nullable=true)
     */
    private $codigobarras;

    /**
     * @var string|null
     *
     * @ORM\Column(name="DIP", type="string", length=45, nullable=true)
     */
    private $dip;

    /**
     * @var string|null
     *
     * @ORM\Column(name="AO", type="string", length=45, nullable=true)
     */
    private $ao;

    /**
     * @var string|null
     *
     * @ORM\Column(name="ACO", type="string", length=45, nullable=true)
     */
    private $aco;

    /**
     * @var string|null
     *
     * @ORM\Column(name="color", type="string", length=45, nullable=true)
     */
    private $color;

    /**
     * @var string|null
     *
     * @ORM\Column(name="observaciones", type="text", length=16777215, nullable=true)
     */
    private $observaciones;

    /**
     * @var string|null
     *
     * @ORM\Column(name="status", type="string", length=1, nullable=true, options={"default"="1","fixed"=true})
     */
    private $status = '1';

    /**
     * @var \DateTime
     *
     * @ORM\Column(name="creacion", type="datetime", nullable=false)
     */
    private $creacion;

    /**
     * @var \DateTime
     *
     * @ORM\Column(name="actualizacion", type="datetime", nullable=false)
     */
    private $actualizacion;

    /**
     * @var string|null
     *
     * @ORM\Column(name="tipoOrden", type="string", length=2, nullable=true, options={"fixed"=true})
     */
    private $tipoorden;

    /**
     * @var int|null
     *
     * @ORM\Column(name="etapa", type="integer", nullable=true, options={"default"="1"})
     */
    private $etapa = 1;

    /**
     * @var string|null
     *
     * @ORM\Column(name="cb", type="string", length=45, nullable=true)
     */
    private $cb;

    /**
     * @var string|null
     *
     * @ORM\Column(name="diam", type="string", length=45, nullable=true)
     */
    private $diam;

    /**
     * @var string|null
     *
     * @ORM\Column(name="tipoLenteContacto", type="string", length=2, nullable=true, options={"fixed"=true})
     */
    private $tipolentecontacto;

    /**
     * @var string|null
     *
     * @ORM\Column(name="addOrdenLaboratorio", type="string", length=45, nullable=true)
     */
    private $addordenlaboratorio;

    /**
     * @var string|null
     *
     * @ORM\Column(name="base", type="string", length=45, nullable=true)
     */
    private $base;

    /**
     * @var string|null
     *
     * @ORM\Column(name="folioAutorizacion", type="string", length=45, nullable=true)
     */
    private $folioautorizacion;

    /**
     * @var string|null
     *
     * @ORM\Column(name="statusAutorizacion", type="string", length=45, nullable=true)
     */
    private $statusautorizacion;

    /**
     * @var \DateTime|null
     *
     * @ORM\Column(name="recepcion", type="datetime", nullable=true)
     */
    private $recepcion;

    /**
     * @var \DateTime|null
     *
     * @ORM\Column(name="fechaLaboratorio", type="datetime", nullable=true)
     */
    private $fechalaboratorio;

    /**
     * @var string|null
     *
     * @ORM\Column(name="statusLaboratorio", type="string", length=45, nullable=true)
     */
    private $statuslaboratorio;

    /**
     * @var \DateTime|null
     *
     * @ORM\Column(name="fechaSalidaLaboratorio", type="datetime", nullable=true)
     */
    private $fechasalidalaboratorio;

    /**
     * @var string|null
     *
     * @ORM\Column(name="estado", type="string", length=45, nullable=true)
     */
    private $estado;

    /**
     * @var string|null
     *
     * @ORM\Column(name="prescriptionFile", type="string", length=45, nullable=true)
     */
    private $prescriptionfile;

    /**
     * @var string
     *
     * @ORM\Column(name="stage", type="string", length=1, nullable=false, options={"default"="1","fixed"=true})
     */
    private $stage = '1';

    /**
     * @var \DateTime|null
     *
     * @ORM\Column(name="receptionDate", type="datetime", nullable=true)
     */
    private $receptiondate;

    /**
     * @var \DateTime|null
     *
     * @ORM\Column(name="elaborationDate", type="datetime", nullable=true)
     */
    private $elaborationdate;

    /**
     * @var string
     *
     * @ORM\Column(name="refusedClient", type="string", length=1, nullable=false, options={"fixed"=true})
     */
    private $refusedclient;

    /**
     * @var \DateTime|null
     *
     * @ORM\Column(name="refuseDate", type="datetime", nullable=true)
     */
    private $refusedate;

    /**
     * @var string|null
     *
     * @ORM\Column(name="refuseReason", type="text", length=65535, nullable=true)
     */
    private $refusereason;

    /**
     * @var string|null
     *
     * @ORM\Column(name="suggestions", type="text", length=65535, nullable=true)
     */
    private $suggestions;

    /**
     * @var string|null
     *
     * @ORM\Column(name="sendglassessupplier", type="string", length=45, nullable=true)
     */
    private $sendglassessupplier;

    /**
     * @var string|null
     *
     * @ORM\Column(name="products", type="text", length=65535, nullable=true)
     */
    private $products;

    /**
     * @var string|null
     *
     * @ORM\Column(name="diagnosis", type="string", length=150, nullable=true)
     */
    private $diagnosis;

    /**
     * @var string|null
     *
     * @ORM\Column(name="notes", type="string", length=150, nullable=true)
     */
    private $notes;

    /**
     * @var \Disenolente
     *
     * @ORM\ManyToOne(targetEntity="Disenolente")
     * @ORM\JoinColumns({
     *   @ORM\JoinColumn(name="disenoLente_iddisenoLente", referencedColumnName="iddisenoLente")
     * })
     */
    private $disenolenteIddisenolente;

    /**
     * @var \Tratamiento
     *
     * @ORM\ManyToOne(targetEntity="Tratamiento")
     * @ORM\JoinColumns({
     *   @ORM\JoinColumn(name="tratamiento_idtratamiento", referencedColumnName="idtratamiento")
     * })
     */
    private $tratamientoIdtratamiento;

    /**
     * @var \Cliente
     *
     * @ORM\ManyToOne(targetEntity="Cliente")
     * @ORM\JoinColumns({
     *   @ORM\JoinColumn(name="cliente_idcliente", referencedColumnName="idcliente")
     * })
     */
    private $clienteIdcliente;

    /**
     * @var \Material
     *
     * @ORM\ManyToOne(targetEntity="Material")
     * @ORM\JoinColumns({
     *   @ORM\JoinColumn(name="material_idmaterial", referencedColumnName="idmaterial")
     * })
     */
    private $materialIdmaterial;

    /**
     * @var \Beneficiario
     *
     * @ORM\ManyToOne(targetEntity="Beneficiario")
     * @ORM\JoinColumns({
     *   @ORM\JoinColumn(name="beneficiario_idbeneficiario", referencedColumnName="idbeneficiario")
     * })
     */
    private $beneficiarioIdbeneficiario;

    /**
     * @var \Flujoexpediente
     *
     * @ORM\ManyToOne(targetEntity="Flujoexpediente")
     * @ORM\JoinColumns({
     *   @ORM\JoinColumn(name="flujoExpediente_idflujoExpediente", referencedColumnName="idflujoExpediente")
     * })
     */
    private $flujoexpedienteIdflujoexpediente;

    /**
     * @var \Proveedor
     *
     * @ORM\ManyToOne(targetEntity="Proveedor")
     * @ORM\JoinColumns({
     *   @ORM\JoinColumn(name="proveedor_idproveedor", referencedColumnName="idproveedor")
     * })
     */
    private $proveedorIdproveedor;

    public function getIdordenlaboratorio(): ?int
    {
        return $this->idordenlaboratorio;
    }

    public function getRxant(): ?string
    {
        return $this->rxant;
    }

    public function setRxant(?string $rxant): self
    {
        $this->rxant = $rxant;

        return $this;
    }

    public function getOjoderecho(): ?string
    {
        return $this->ojoderecho;
    }

    public function setOjoderecho(?string $ojoderecho): self
    {
        $this->ojoderecho = $ojoderecho;

        return $this;
    }

    public function getOjoizquierdo(): ?string
    {
        return $this->ojoizquierdo;
    }

    public function setOjoizquierdo(?string $ojoizquierdo): self
    {
        $this->ojoizquierdo = $ojoizquierdo;

        return $this;
    }

    public function getEsferaod(): ?string
    {
        return $this->esferaod;
    }

    public function setEsferaod(?string $esferaod): self
    {
        $this->esferaod = $esferaod;

        return $this;
    }

    public function getEsferaoi(): ?string
    {
        return $this->esferaoi;
    }

    public function setEsferaoi(?string $esferaoi): self
    {
        $this->esferaoi = $esferaoi;

        return $this;
    }

    public function getCilindrood(): ?string
    {
        return $this->cilindrood;
    }

    public function setCilindrood(?string $cilindrood): self
    {
        $this->cilindrood = $cilindrood;

        return $this;
    }

    public function getCilindrooi(): ?string
    {
        return $this->cilindrooi;
    }

    public function setCilindrooi(?string $cilindrooi): self
    {
        $this->cilindrooi = $cilindrooi;

        return $this;
    }

    public function getEjeod(): ?string
    {
        return $this->ejeod;
    }

    public function setEjeod(?string $ejeod): self
    {
        $this->ejeod = $ejeod;

        return $this;
    }

    public function getEjeoi(): ?string
    {
        return $this->ejeoi;
    }

    public function setEjeoi(?string $ejeoi): self
    {
        $this->ejeoi = $ejeoi;

        return $this;
    }

    public function getAvlejosod(): ?string
    {
        return $this->avlejosod;
    }

    public function setAvlejosod(?string $avlejosod): self
    {
        $this->avlejosod = $avlejosod;

        return $this;
    }

    public function getAvlejosoi(): ?string
    {
        return $this->avlejosoi;
    }

    public function setAvlejosoi(?string $avlejosoi): self
    {
        $this->avlejosoi = $avlejosoi;

        return $this;
    }

    public function getAvcercasaddod(): ?string
    {
        return $this->avcercasaddod;
    }

    public function setAvcercasaddod(?string $avcercasaddod): self
    {
        $this->avcercasaddod = $avcercasaddod;

        return $this;
    }

    public function getAvcercasaddoi(): ?string
    {
        return $this->avcercasaddoi;
    }

    public function setAvcercasaddoi(?string $avcercasaddoi): self
    {
        $this->avcercasaddoi = $avcercasaddoi;

        return $this;
    }

    public function getAvcercacaddod(): ?string
    {
        return $this->avcercacaddod;
    }

    public function setAvcercacaddod(?string $avcercacaddod): self
    {
        $this->avcercacaddod = $avcercacaddod;

        return $this;
    }

    public function getAvcercacaddoi(): ?string
    {
        return $this->avcercacaddoi;
    }

    public function setAvcercacaddoi(?string $avcercacaddoi): self
    {
        $this->avcercacaddoi = $avcercacaddoi;

        return $this;
    }

    public function getArmazoncliente(): ?string
    {
        return $this->armazoncliente;
    }

    public function setArmazoncliente(?string $armazoncliente): self
    {
        $this->armazoncliente = $armazoncliente;

        return $this;
    }

    public function getCodigobarras(): ?string
    {
        return $this->codigobarras;
    }

    public function setCodigobarras(?string $codigobarras): self
    {
        $this->codigobarras = $codigobarras;

        return $this;
    }

    public function getDip(): ?string
    {
        return $this->dip;
    }

    public function setDip(?string $dip): self
    {
        $this->dip = $dip;

        return $this;
    }

    public function getAo(): ?string
    {
        return $this->ao;
    }

    public function setAo(?string $ao): self
    {
        $this->ao = $ao;

        return $this;
    }

    public function getAco(): ?string
    {
        return $this->aco;
    }

    public function setAco(?string $aco): self
    {
        $this->aco = $aco;

        return $this;
    }

    public function getColor(): ?string
    {
        return $this->color;
    }

    public function setColor(?string $color): self
    {
        $this->color = $color;

        return $this;
    }

    public function getObservaciones(): ?string
    {
        return $this->observaciones;
    }

    public function setObservaciones(?string $observaciones): self
    {
        $this->observaciones = $observaciones;

        return $this;
    }

    public function getStatus(): ?string
    {
        return $this->status;
    }

    public function setStatus(?string $status): self
    {
        $this->status = $status;

        return $this;
    }

    public function getCreacion(): ?\DateTimeInterface
    {
        return $this->creacion;
    }

    public function setCreacion(\DateTimeInterface $creacion): self
    {
        $this->creacion = $creacion;

        return $this;
    }

    public function getActualizacion(): ?\DateTimeInterface
    {
        return $this->actualizacion;
    }

    public function setActualizacion(\DateTimeInterface $actualizacion): self
    {
        $this->actualizacion = $actualizacion;

        return $this;
    }

    public function getTipoorden(): ?string
    {
        return $this->tipoorden;
    }

    public function setTipoorden(?string $tipoorden): self
    {
        $this->tipoorden = $tipoorden;

        return $this;
    }

    public function getEtapa(): ?int
    {
        return $this->etapa;
    }

    public function setEtapa(?int $etapa): self
    {
        $this->etapa = $etapa;

        return $this;
    }

    public function getCb(): ?string
    {
        return $this->cb;
    }

    public function setCb(?string $cb): self
    {
        $this->cb = $cb;

        return $this;
    }

    public function getDiam(): ?string
    {
        return $this->diam;
    }

    public function setDiam(?string $diam): self
    {
        $this->diam = $diam;

        return $this;
    }

    public function getTipolentecontacto(): ?string
    {
        return $this->tipolentecontacto;
    }

    public function setTipolentecontacto(?string $tipolentecontacto): self
    {
        $this->tipolentecontacto = $tipolentecontacto;

        return $this;
    }

    public function getAddordenlaboratorio(): ?string
    {
        return $this->addordenlaboratorio;
    }

    public function setAddordenlaboratorio(?string $addordenlaboratorio): self
    {
        $this->addordenlaboratorio = $addordenlaboratorio;

        return $this;
    }

    public function getBase(): ?string
    {
        return $this->base;
    }

    public function setBase(?string $base): self
    {
        $this->base = $base;

        return $this;
    }

    public function getFolioautorizacion(): ?string
    {
        return $this->folioautorizacion;
    }

    public function setFolioautorizacion(?string $folioautorizacion): self
    {
        $this->folioautorizacion = $folioautorizacion;

        return $this;
    }

    public function getStatusautorizacion(): ?string
    {
        return $this->statusautorizacion;
    }

    public function setStatusautorizacion(?string $statusautorizacion): self
    {
        $this->statusautorizacion = $statusautorizacion;

        return $this;
    }

    public function getRecepcion(): ?\DateTimeInterface
    {
        return $this->recepcion;
    }

    public function setRecepcion(?\DateTimeInterface $recepcion): self
    {
        $this->recepcion = $recepcion;

        return $this;
    }

    public function getFechalaboratorio(): ?\DateTimeInterface
    {
        return $this->fechalaboratorio;
    }

    public function setFechalaboratorio(?\DateTimeInterface $fechalaboratorio): self
    {
        $this->fechalaboratorio = $fechalaboratorio;

        return $this;
    }

    public function getStatuslaboratorio(): ?string
    {
        return $this->statuslaboratorio;
    }

    public function setStatuslaboratorio(?string $statuslaboratorio): self
    {
        $this->statuslaboratorio = $statuslaboratorio;

        return $this;
    }

    public function getFechasalidalaboratorio(): ?\DateTimeInterface
    {
        return $this->fechasalidalaboratorio;
    }

    public function setFechasalidalaboratorio(?\DateTimeInterface $fechasalidalaboratorio): self
    {
        $this->fechasalidalaboratorio = $fechasalidalaboratorio;

        return $this;
    }

    public function getEstado(): ?string
    {
        return $this->estado;
    }

    public function setEstado(?string $estado): self
    {
        $this->estado = $estado;

        return $this;
    }

    public function getPrescriptionfile(): ?string
    {
        return $this->prescriptionfile;
    }

    public function setPrescriptionfile(?string $prescriptionfile): self
    {
        $this->prescriptionfile = $prescriptionfile;

        return $this;
    }

    public function getStage(): ?string
    {
        return $this->stage;
    }

    public function setStage(string $stage): self
    {
        $this->stage = $stage;

        return $this;
    }

    public function getReceptiondate(): ?\DateTimeInterface
    {
        return $this->receptiondate;
    }

    public function setReceptiondate(?\DateTimeInterface $receptiondate): self
    {
        $this->receptiondate = $receptiondate;

        return $this;
    }

    public function getElaborationdate(): ?\DateTimeInterface
    {
        return $this->elaborationdate;
    }

    public function setElaborationdate(?\DateTimeInterface $elaborationdate): self
    {
        $this->elaborationdate = $elaborationdate;

        return $this;
    }

    public function getRefusedclient(): ?string
    {
        return $this->refusedclient;
    }

    public function setRefusedclient(string $refusedclient): self
    {
        $this->refusedclient = $refusedclient;

        return $this;
    }

    public function getRefusedate(): ?\DateTimeInterface
    {
        return $this->refusedate;
    }

    public function setRefusedate(?\DateTimeInterface $refusedate): self
    {
        $this->refusedate = $refusedate;

        return $this;
    }

    public function getRefusereason(): ?string
    {
        return $this->refusereason;
    }

    public function setRefusereason(?string $refusereason): self
    {
        $this->refusereason = $refusereason;

        return $this;
    }

    public function getSuggestions(): ?string
    {
        return $this->suggestions;
    }

    public function setSuggestions(?string $suggestions): self
    {
        $this->suggestions = $suggestions;

        return $this;
    }

    public function getSendglassessupplier(): ?string
    {
        return $this->sendglassessupplier;
    }

    public function setSendglassessupplier(?string $sendglassessupplier): self
    {
        $this->sendglassessupplier = $sendglassessupplier;

        return $this;
    }

    public function getProducts(): ?string
    {
        return $this->products;
    }

    public function setProducts(?string $products): self
    {
        $this->products = $products;

        return $this;
    }

    public function getDiagnosis(): ?string
    {
        return $this->diagnosis;
    }

    public function setDiagnosis(?string $diagnosis): self
    {
        $this->diagnosis = $diagnosis;

        return $this;
    }

    public function getNotes(): ?string
    {
        return $this->notes;
    }

    public function setNotes(?string $notes): self
    {
        $this->notes = $notes;

        return $this;
    }

    public function getDisenolenteIddisenolente(): ?Disenolente
    {
        return $this->disenolenteIddisenolente;
    }

    public function setDisenolenteIddisenolente(?Disenolente $disenolenteIddisenolente): self
    {
        $this->disenolenteIddisenolente = $disenolenteIddisenolente;

        return $this;
    }

    public function getTratamientoIdtratamiento(): ?Tratamiento
    {
        return $this->tratamientoIdtratamiento;
    }

    public function setTratamientoIdtratamiento(?Tratamiento $tratamientoIdtratamiento): self
    {
        $this->tratamientoIdtratamiento = $tratamientoIdtratamiento;

        return $this;
    }

    public function getClienteIdcliente(): ?Cliente
    {
        return $this->clienteIdcliente;
    }

    public function setClienteIdcliente(?Cliente $clienteIdcliente): self
    {
        $this->clienteIdcliente = $clienteIdcliente;

        return $this;
    }

    public function getMaterialIdmaterial(): ?Material
    {
        return $this->materialIdmaterial;
    }

    public function setMaterialIdmaterial(?Material $materialIdmaterial): self
    {
        $this->materialIdmaterial = $materialIdmaterial;

        return $this;
    }

    public function getBeneficiarioIdbeneficiario(): ?Beneficiario
    {
        return $this->beneficiarioIdbeneficiario;
    }

    public function setBeneficiarioIdbeneficiario(?Beneficiario $beneficiarioIdbeneficiario): self
    {
        $this->beneficiarioIdbeneficiario = $beneficiarioIdbeneficiario;

        return $this;
    }

    public function getFlujoexpedienteIdflujoexpediente(): ?Flujoexpediente
    {
        return $this->flujoexpedienteIdflujoexpediente;
    }

    public function setFlujoexpedienteIdflujoexpediente(?Flujoexpediente $flujoexpedienteIdflujoexpediente): self
    {
        $this->flujoexpedienteIdflujoexpediente = $flujoexpedienteIdflujoexpediente;

        return $this;
    }

    public function getProveedorIdproveedor(): ?Proveedor
    {
        return $this->proveedorIdproveedor;
    }

    public function setProveedorIdproveedor(?Proveedor $proveedorIdproveedor): self
    {
        $this->proveedorIdproveedor = $proveedorIdproveedor;

        return $this;
    }
public function getStockventaordenlaboratorios(): Collection
{
    return $this->stockventaordenlaboratorios;
}


}
