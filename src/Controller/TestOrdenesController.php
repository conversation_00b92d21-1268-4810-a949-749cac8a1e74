<?php

namespace App\Controller;

use Symfony\Bundle\FrameworkBundle\Controller\AbstractController;
use Symfony\Component\HttpFoundation\JsonResponse;
use Symfony\Component\HttpFoundation\Request;
use Symfony\Component\Routing\Annotation\Route;
use Doctrine\ORM\EntityManagerInterface;

class TestOrdenesController extends AbstractController
{
    private $entityManager;

    public function __construct(EntityManagerInterface $entityManager)
    {
        $this->entityManager = $entityManager;
    }

    /**
     * @Route("/test/ordenes/basica", name="test_ordenes_basica", methods={"GET"})
     */
    public function testInformacionBasica(Request $request): JsonResponse
    {
        try {
            $limite = $request->query->get('limite', 5);
            
            $sql = "SELECT 
                ol.idordenlaboratorio,
                GROUP_CONCAT(DISTINCT v.folio ORDER BY v.folio SEPARATOR ', ') as folios,
                CASE 
                    WHEN v.cotizacion = '1' THEN 'Cotización'
                    ELSE 'Venta'
                E<PERSON> as estado_venta,
                suc_flujo.nombre as sucursal_flujo,
                c.nombre as cliente_nombre,
                c.apellidopaterno as cliente_apellido_paterno,
                ol.esferaod as esfera_od,
                ol.esferaoi as esfera_oi,
                ol.creacion as fecha_creacion_orden
                
            FROM ordenLaboratorio ol
            INNER JOIN flujoExpediente fe ON ol.flujoExpediente_idflujoExpediente = fe.idflujoExpediente
            INNER JOIN cliente c ON fe.cliente_idcliente = c.idcliente
            LEFT JOIN sucursal suc_flujo ON fe.sucursal_idsucursal = suc_flujo.idsucursal
            LEFT JOIN stockVentaOrdenLaboratorio svol ON svol.ordenLaboratorio_idordenLaboratorio = ol.idordenLaboratorio
            LEFT JOIN stockVenta sv ON svol.stockVenta_idstockVenta = sv.idstockVenta
            LEFT JOIN venta v ON sv.venta_idventa = v.idventa
            
            WHERE ol.status = '1'
            GROUP BY ol.idordenLaboratorio
            ORDER BY ol.creacion DESC
            LIMIT ?";

            $stmt = $this->entityManager->getConnection()->prepare($sql);
            $stmt->bindValue(1, (int)$limite, \PDO::PARAM_INT);
            $result = $stmt->executeQuery()->fetchAllAssociative();
            
            return new JsonResponse([
                'success' => true,
                'data' => $result,
                'total' => count($result),
                'message' => 'Información básica obtenida correctamente'
            ]);
            
        } catch (\Exception $e) {
            return new JsonResponse([
                'success' => false,
                'error' => $e->getMessage(),
                'message' => 'Error al obtener información básica'
            ], 500);
        }
    }

    /**
     * @Route("/test/ordenes/completa", name="test_ordenes_completa", methods={"GET"})
     */
    public function testInformacionCompleta(Request $request): JsonResponse
    {
        try {
            $limite = $request->query->get('limite', 3);
            
            $sql = "SELECT DISTINCT
                -- Folios de venta
                GROUP_CONCAT(DISTINCT v.folio ORDER BY v.folio SEPARATOR ', ') as folios,
                
                -- Estado de venta
                CASE 
                    WHEN v.cotizacion = '1' THEN 'Cotización'
                    ELSE 'Venta'
                END as estado_venta,
                
                -- Sucursales
                suc_flujo.nombre as sucursal_flujo,
                suc_venta.nombre as sucursal_venta,
                
                -- Fechas
                fe.creacion as fecha_creacion_flujo,
                ol.creacion as fecha_creacion_orden,
                ol.actualizacion as fecha_actualizacion_orden,
                v.fecha as fecha_venta,
                
                -- Tipo de venta
                tv.nombre as tipo_venta,
                
                -- Información del cliente
                c.nombre as cliente_nombre,
                c.apellidopaterno as cliente_apellido_paterno,
                c.apellidomaterno as cliente_apellido_materno,
                v.beneficiario as beneficiario,
                c.numeroempleado as numero_empleado,
                ec.nombre as empresa,
                u.nombre as unidad,
                
                -- Optometrista
                CONCAT(usr.nombre, ' ', usr.apellidopaterno, ' ', usr.apellidomaterno) as optometrista,
                
                -- Información de la orden de laboratorio
                CASE 
                    WHEN ol.tipoorden = '1' THEN 'Armazón'
                    WHEN ol.tipoorden = '2' THEN 'Lente de contacto'
                    ELSE 'No especificado'
                END as tipo_orden,
                
                ol.esferaod as esfera_od,
                ol.esferaoi as esfera_oi,
                ol.cilindrood as cilindro_od,
                ol.cilindrooi as cilindro_oi,
                ol.ejeod as eje_od,
                ol.ejeoi as eje_oi,
                ol.dip as dip,
                ol.ao as ao,
                ol.aco as aco,
                
                -- Graduación subjetiva final
                g.esfodsf as subjetiva_final_esfera_od,
                g.esfoisf as subjetiva_final_esfera_oi,
                g.cilodsf as subjetiva_final_cilindro_od,
                g.ciloisf as subjetiva_final_cilindro_oi,
                g.ejeodsf as subjetiva_final_eje_od,
                g.ejeoisf as subjetiva_final_eje_oi,
                g.avlodsf as subjetiva_final_av_lejos_od,
                g.avloisf as subjetiva_final_av_lejos_oi,
                g.avcsaodsf as subjetiva_final_av_cerca_sadd_od,
                g.avcsaoisf as subjetiva_final_av_cerca_sadd_oi,
                g.avccaodsf as subjetiva_final_av_cerca_cadd_od,
                g.avccaoisf as subjetiva_final_av_cerca_cadd_oi,
                
                -- Información específica de lentes de contacto
                CASE 
                    WHEN ol.tipolentecontacto = '1' THEN 'Blando'
                    WHEN ol.tipolentecontacto = '2' THEN 'Rígido'
                    ELSE 'No especificado'
                END as tipo_lente_contacto,
                ol.cb as curva_base,
                ol.diam as diametro,
                
                -- Información adicional
                ol.armazoncliente as armazon_propio_cliente,
                ol.observaciones as observaciones,
                dl.nombre as diseno,
                m.nombre as material,
                tr.nombre as tratamiento,
                ol.suggestions as sugerencias,
                
                -- Productos
                GROUP_CONCAT(DISTINCT CONCAT(p.nombre, ' - ', p.modelo) ORDER BY p.nombre SEPARATOR ', ') as productos_venta,
                ol.products as productos_orden
                
            FROM ordenLaboratorio ol
            INNER JOIN flujoExpediente fe ON ol.flujoExpediente_idflujoExpediente = fe.idflujoExpediente
            INNER JOIN cliente c ON fe.cliente_idcliente = c.idcliente
            LEFT JOIN empresaCliente ec ON c.empresaCliente_idempresaCliente = ec.idempresaCliente
            LEFT JOIN unidad u ON c.unidad_idunidad = u.idunidad
            LEFT JOIN usuario usr ON fe.usuario_idusuario = usr.idusuario
            LEFT JOIN sucursal suc_flujo ON fe.sucursal_idsucursal = suc_flujo.idsucursal
            LEFT JOIN venta v ON fe.venta_idventa = v.idventa
            LEFT JOIN sucursal suc_venta ON v.sucursal_idsucursal = suc_venta.idsucursal
            LEFT JOIN tipoVenta tv ON v.tipoVenta_idtipoVenta = tv.idtipoVenta
            LEFT JOIN stockVentaOrdenLaboratorio svol ON svol.ordenLaboratorio_idordenLaboratorio = ol.idordenLaboratorio
            LEFT JOIN stockVenta sv ON svol.stockVenta_idstockVenta = sv.idstockVenta
            LEFT JOIN stock st ON sv.stock_idstock = st.idstock
            LEFT JOIN producto p ON st.producto_idproducto = p.idproducto
            LEFT JOIN graduacion g ON fe.graduacion_idgraduacion = g.idgraduacion
            LEFT JOIN disenoLente dl ON ol.disenoLente_iddisenoLente = dl.iddisenoLente
            LEFT JOIN material m ON ol.material_idmaterial = m.idmaterial
            LEFT JOIN tratamiento tr ON ol.tratamiento_idtratamiento = tr.idtratamiento
            
            WHERE ol.status = '1'
            GROUP BY ol.idordenLaboratorio
            ORDER BY ol.creacion DESC
            LIMIT ?";

            $stmt = $this->entityManager->getConnection()->prepare($sql);
            $stmt->bindValue(1, (int)$limite, \PDO::PARAM_INT);
            $result = $stmt->executeQuery()->fetchAllAssociative();
            
            return new JsonResponse([
                'success' => true,
                'data' => $result,
                'total' => count($result),
                'message' => 'Información completa obtenida correctamente'
            ]);
            
        } catch (\Exception $e) {
            return new JsonResponse([
                'success' => false,
                'error' => $e->getMessage(),
                'message' => 'Error al obtener información completa'
            ], 500);
        }
    }

    /**
     * @Route("/test/ordenes/folios/{id}", name="test_ordenes_folios", methods={"GET"})
     */
    public function testFoliosPorOrden(int $id): JsonResponse
    {
        try {
            $sql = "SELECT DISTINCT v.folio 
                    FROM stockVentaOrdenLaboratorio svol
                    INNER JOIN stockVenta sv ON svol.stockVenta_idstockVenta = sv.idstockVenta
                    INNER JOIN venta v ON sv.venta_idventa = v.idventa
                    WHERE svol.ordenLaboratorio_idordenLaboratorio = ? 
                    AND v.status = '1'
                    ORDER BY v.folio";

            $stmt = $this->entityManager->getConnection()->prepare($sql);
            $result = $stmt->executeQuery([$id])->fetchAllAssociative();
            
            return new JsonResponse([
                'success' => true,
                'orden_id' => $id,
                'folios' => array_column($result, 'folio'),
                'total_folios' => count($result),
                'message' => 'Folios obtenidos correctamente'
            ]);
            
        } catch (\Exception $e) {
            return new JsonResponse([
                'success' => false,
                'error' => $e->getMessage(),
                'message' => 'Error al obtener folios'
            ], 500);
        }
    }

    /**
     * @Route("/test/ordenes/export", name="test_ordenes_export", methods={"GET"})
     */
    public function exportarDatosCompletos(Request $request): JsonResponse
    {
        try {
            $limite = $request->query->get('limite', 10);
            $fechaInicio = $request->query->get('fecha_inicio');
            $fechaFin = $request->query->get('fecha_fin');

            $sql = "SELECT DISTINCT
                -- ID de la orden
                ol.idordenLaboratorio as id_orden,

                -- Folios de venta
                GROUP_CONCAT(DISTINCT v.folio ORDER BY v.folio SEPARATOR ', ') as folios,

                -- Estado de venta
                CASE
                    WHEN v.cotizacion = '1' THEN 'Cotización'
                    ELSE 'Venta'
                END as estado_venta,

                -- Sucursales
                suc_flujo.nombre as sucursal_flujo,
                suc_venta.nombre as sucursal_venta,

                -- Fechas (formateadas)
                DATE_FORMAT(fe.creacion, '%d/%m/%Y %H:%i') as fecha_creacion_flujo,
                DATE_FORMAT(ol.creacion, '%d/%m/%Y %H:%i') as fecha_creacion_orden,
                DATE_FORMAT(ol.actualizacion, '%d/%m/%Y %H:%i') as fecha_actualizacion_orden,
                DATE_FORMAT(v.fecha, '%d/%m/%Y %H:%i') as fecha_venta,

                -- Tipo de venta
                COALESCE(tv.nombre, 'No especificado') as tipo_venta,

                -- Información del cliente
                c.nombre as nombre,
                c.apellidopaterno as apellido_paterno,
                c.apellidomaterno as apellido_materno,
                COALESCE(v.beneficiario, 'No especificado') as beneficiario,
                COALESCE(c.numeroempleado, 'No especificado') as numero_empleado,
                COALESCE(ec.nombre, 'Sin empresa') as empresa,
                COALESCE(u.nombre, 'No especificado') as unidad,

                -- Optometrista
                COALESCE(CONCAT(usr.nombre, ' ', usr.apellidopaterno, ' ', usr.apellidomaterno), 'No especificado') as optometrista,

                -- Información de la orden de laboratorio
                CASE
                    WHEN ol.tipoorden = '1' THEN 'Armazón'
                    WHEN ol.tipoorden = '2' THEN 'Lente de contacto'
                    ELSE 'No especificado'
                END as tipo_orden,

                COALESCE(ol.esferaod, 'N/A') as esfera_od,
                COALESCE(ol.esferaoi, 'N/A') as esfera_oi,
                COALESCE(ol.cilindrood, 'N/A') as cilindro_od,
                COALESCE(ol.cilindrooi, 'N/A') as cilindro_oi,
                COALESCE(ol.ejeod, 'N/A') as eje_od,
                COALESCE(ol.ejeoi, 'N/A') as eje_oi,
                COALESCE(ol.dip, 'N/A') as dip,
                COALESCE(ol.ao, 'N/A') as ao,
                COALESCE(ol.aco, 'N/A') as aco,

                -- Graduación subjetiva final
                COALESCE(g.esfodsf, 'N/A') as subjetiva_final_esfera_od,
                COALESCE(g.esfoisf, 'N/A') as subjetiva_final_esfera_oi,
                COALESCE(g.cilodsf, 'N/A') as subjetiva_final_cilindro_od,
                COALESCE(g.ciloisf, 'N/A') as subjetiva_final_cilindro_oi,
                COALESCE(g.ejeodsf, 'N/A') as subjetiva_final_eje_od,
                COALESCE(g.ejeoisf, 'N/A') as subjetiva_final_eje_oi,
                COALESCE(g.avlodsf, 'N/A') as subjetiva_final_av_lejos_od,
                COALESCE(g.avloisf, 'N/A') as subjetiva_final_av_lejos_oi,
                COALESCE(g.avcsaodsf, 'N/A') as subjetiva_final_av_cerca_sadd_od,
                COALESCE(g.avcsaoisf, 'N/A') as subjetiva_final_av_cerca_sadd_oi,
                COALESCE(g.avccaodsf, 'N/A') as subjetiva_final_av_cerca_cadd_od,
                COALESCE(g.avccaoisf, 'N/A') as subjetiva_final_av_cerca_cadd_oi,

                -- Información específica de lentes de contacto
                CASE
                    WHEN ol.tipolentecontacto = '1' THEN 'Blando'
                    WHEN ol.tipolentecontacto = '2' THEN 'Rígido'
                    ELSE 'No especificado'
                END as tipo_lente_contacto,
                COALESCE(ol.cb, 'N/A') as curva_base,
                COALESCE(ol.diam, 'N/A') as diametro,

                -- Información adicional
                CASE
                    WHEN ol.armazoncliente = '1' THEN 'Sí'
                    WHEN ol.armazoncliente = '0' THEN 'No'
                    ELSE 'No especificado'
                END as armazon_propio_cliente,
                COALESCE(ol.observaciones, 'Sin observaciones') as observaciones,
                COALESCE(dl.nombre, 'No especificado') as diseno,
                COALESCE(m.nombre, 'No especificado') as material,
                COALESCE(tr.nombre, 'No especificado') as tratamiento,
                COALESCE(ol.suggestions, 'Sin sugerencias') as sugerencias,

                -- Productos
                COALESCE(GROUP_CONCAT(DISTINCT CONCAT(p.nombre, ' - ', p.modelo) ORDER BY p.nombre SEPARATOR ', '), 'Sin productos') as productos_venta,
                COALESCE(ol.products, 'Sin productos específicos') as productos_orden

            FROM ordenLaboratorio ol
            INNER JOIN flujoExpediente fe ON ol.flujoExpediente_idflujoExpediente = fe.idflujoExpediente
            INNER JOIN cliente c ON fe.cliente_idcliente = c.idcliente
            LEFT JOIN empresaCliente ec ON c.empresaCliente_idempresaCliente = ec.idempresaCliente
            LEFT JOIN unidad u ON c.unidad_idunidad = u.idunidad
            LEFT JOIN usuario usr ON fe.usuario_idusuario = usr.idusuario
            LEFT JOIN sucursal suc_flujo ON fe.sucursal_idsucursal = suc_flujo.idsucursal
            LEFT JOIN venta v ON fe.venta_idventa = v.idventa
            LEFT JOIN sucursal suc_venta ON v.sucursal_idsucursal = suc_venta.idsucursal
            LEFT JOIN tipoVenta tv ON v.tipoVenta_idtipoVenta = tv.idtipoVenta
            LEFT JOIN stockVentaOrdenLaboratorio svol ON svol.ordenLaboratorio_idordenLaboratorio = ol.idordenLaboratorio
            LEFT JOIN stockVenta sv ON svol.stockVenta_idstockVenta = sv.idstockVenta
            LEFT JOIN stock st ON sv.stock_idstock = st.idstock
            LEFT JOIN producto p ON st.producto_idproducto = p.idproducto
            LEFT JOIN graduacion g ON fe.graduacion_idgraduacion = g.idgraduacion
            LEFT JOIN disenoLente dl ON ol.disenoLente_iddisenoLente = dl.iddisenoLente
            LEFT JOIN material m ON ol.material_idmaterial = m.idmaterial
            LEFT JOIN tratamiento tr ON ol.tratamiento_idtratamiento = tr.idtratamiento

            WHERE ol.status = '1'";

            // Agregar filtros de fecha si se proporcionan
            if ($fechaInicio && $fechaFin) {
                $sql .= " AND ol.creacion BETWEEN :fecha_inicio AND :fecha_fin";
            }

            $sql .= " GROUP BY ol.idordenLaboratorio
                     ORDER BY ol.creacion DESC
                     LIMIT :limite";

            $stmt = $this->entityManager->getConnection()->prepare($sql);

            // Establecer parámetros
            if ($fechaInicio && $fechaFin) {
                $stmt->bindValue('fecha_inicio', $fechaInicio);
                $stmt->bindValue('fecha_fin', $fechaFin);
            }
            $stmt->bindValue('limite', (int)$limite, \PDO::PARAM_INT);

            $result = $stmt->executeQuery()->fetchAllAssociative();

            return new JsonResponse([
                'success' => true,
                'data' => $result,
                'total' => count($result),
                'filtros_aplicados' => [
                    'limite' => $limite,
                    'fecha_inicio' => $fechaInicio,
                    'fecha_fin' => $fechaFin
                ],
                'message' => 'Datos completos exportados correctamente'
            ]);

        } catch (\Exception $e) {
            return new JsonResponse([
                'success' => false,
                'error' => $e->getMessage(),
                'message' => 'Error al exportar datos completos'
            ], 500);
        }
    }

    /**
     * @Route("/test/ordenes/debug/{id}", name="test_ordenes_debug", methods={"GET"})
     */
    public function debugOrdenEspecifica(int $id): JsonResponse
    {
        try {
            // Obtener la orden con todas las relaciones
            $orden = $this->entityManager->getRepository(\App\Entity\Ordenlaboratorio::class)->find($id);

            if (!$orden) {
                return new JsonResponse([
                    'success' => false,
                    'message' => 'Orden no encontrada'
                ], 404);
            }

            $debug = [
                'orden_id' => $orden->getIdordenlaboratorio(),
                'tiene_flujo_expediente' => $orden->getFlujoexpedienteIdflujoexpediente() ? 'SÍ' : 'NO',
                'tiene_cliente' => null,
                'tiene_venta' => null,
                'tiene_graduacion' => null,
                'tiene_diseno' => $orden->getDisenolenteIddisenolente() ? 'SÍ' : 'NO',
                'tiene_material' => $orden->getMaterialIdmaterial() ? 'SÍ' : 'NO',
                'tiene_tratamiento' => $orden->getTratamientoIdtratamiento() ? 'SÍ' : 'NO',
                'datos_basicos' => [
                    'esfera_od' => $orden->getEsferaod(),
                    'esfera_oi' => $orden->getEsferaoi(),
                    'tipo_orden' => $orden->getTipoorden(),
                    'observaciones' => $orden->getObservaciones() ? 'SÍ' : 'NO',
                    'suggestions' => $orden->getSuggestions() ? 'SÍ' : 'NO',
                ]
            ];

            // Verificar flujo expediente
            if ($orden->getFlujoexpedienteIdflujoexpediente()) {
                $flujo = $orden->getFlujoexpedienteIdflujoexpediente();
                $debug['tiene_cliente'] = $flujo->getClienteIdcliente() ? 'SÍ' : 'NO';
                $debug['tiene_venta'] = $flujo->getVentaIdventa() ? 'SÍ' : 'NO';
                $debug['tiene_graduacion'] = $flujo->getGraduacionIdgraduacion() ? 'SÍ' : 'NO';

                if ($flujo->getClienteIdcliente()) {
                    $cliente = $flujo->getClienteIdcliente();
                    $debug['cliente_info'] = [
                        'nombre' => $cliente->getNombre(),
                        'apellido_paterno' => $cliente->getApellidopaterno(),
                        'tiene_empresa' => $cliente->getEmpresaclienteIdempresacliente() ? 'SÍ' : 'NO',
                        'tiene_unidad' => $cliente->getUnidadIdunidad() ? 'SÍ' : 'NO',
                    ];
                }
            }

            return new JsonResponse([
                'success' => true,
                'debug' => $debug,
                'message' => 'Debug completado'
            ]);

        } catch (\Exception $e) {
            return new JsonResponse([
                'success' => false,
                'error' => $e->getMessage(),
                'message' => 'Error en debug'
            ], 500);
        }
    }
}
