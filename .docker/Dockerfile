# Imagen base
FROM php:7.4-apache

# Copia del proyecto al contenedor
COPY . /var/www/html

# Crear directorios necesarios
RUN mkdir -p /var/www/html/var/cache/ \
    /var/www/html/var/cache/dev \
    /var/www/html/var/cache/prod \
    /var/www/html/var/log \
    && chmod -R 777 /var/www/html/var/ \
    && chmod -R 777 /var/www/html/var/log \
    && chmod -R 777 /var/www/html/var/cache/

# Configuración de Apache y PHP
COPY .docker/apache/000-default.conf /etc/apache2/sites-available/
COPY .docker/apache/apache2.conf /etc/apache2/
COPY .docker/php/php.ini /usr/local/etc/php/

# Cliente de facturación
COPY vendor/facturama/facturama-php-sdk/src/Client.php /var/www/html/config/facturama/

# Instalación de dependencias del sistema
RUN apt-get update && apt-get install -y \
    libzip-dev \
    libfreetype6-dev \
    libjpeg62-turbo-dev \
    libpng-dev \
    libicu-dev \
    unzip \
    git \
    && docker-php-ext-configure gd --with-freetype --with-jpeg \
    && docker-php-ext-install -j$(nproc) gd intl pdo_mysql zip

# Instalación y habilitación de Xdebug
RUN pecl install xdebug-3.1.6 \
    && docker-php-ext-enable xdebug

# Permisos y ownership
RUN chmod -R 777 /var/www/html/public /var/www/html/var \
    && chown -R www-data:www-data /var/www/html

# Habilitar mod_rewrite en Apache
RUN a2enmod rewrite

# Composer configuración
ENV COMPOSER_ALLOW_SUPERUSER=1

# Instalar Composer
RUN curl -sS https://getcomposer.org/installer | php -- \
    --install-dir=/usr/local/bin --filename=composer \
    && composer --version

# Git confianza para evitar errores de permisos
RUN git config --system --add safe.directory /var/www/html

# Cache de composer
RUN mkdir -p /var/www/.composer \
    && chown -R www-data:www-data /var/www/.composer

# Instalar dependencias PHP como www-data
USER www-data
WORKDIR /var/www/html

# Instala dependencias
RUN composer install --no-scripts --no-interaction --prefer-dist

# Volver a root para pasos finales (si hicieran falta)
USER root

# Ownership final
RUN chown -R www-data:www-data /var/www/html