# Campos de Exportación - Órdenes de Laboratorio

## Resumen de Cambios Realizados

### Problema Original
El sistema estaba obteniendo los folios de venta a través de `flujoexpedienteventa`, pero se necesitaba obtenerlos directamente desde `stockventaordenlaboratorio`.

### Solución Implementada
1. **Modificado el método `getFoliosFromStocks`** para usar la ruta correcta:
   ```
   stockVentaOrdenLaboratorio -> stockVenta -> venta
   ```

2. **Agregados métodos auxiliares** para formatear datos y obtener información adicional.

## Campos Disponibles para Exportación

### Información Básica de la Venta
- **Folio(s)**: Folios de las ventas relacionadas (obtenidos desde stockventaordenlaboratorio)
- **Estado de venta**: Estado actual de la venta (activa/cotización)
- **Sucursal del flujo**: Sucursal donde se creó el flujo de expediente
- **Sucursal de la venta**: Sucursal donde se realizó la venta
- **Fecha de creación del flujo**: <PERSON>uándo se creó el expediente
- **Fecha de creación de la orden**: Cuándo se creó la orden de laboratorio
- **Fecha de actualización de la orden**: Última modificación de la orden
- **Fecha de venta**: Cuándo se realizó la venta
- **Tipo de venta**: Tipo de transacción (normal, especial, etc.)

### Información del Cliente
- **Nombre**: Nombre del cliente
- **Apellido paterno**: Apellido paterno del cliente
- **Apellido materno**: Apellido materno del cliente
- **Beneficiario**: Beneficiario de la venta (si aplica)
- **Número de empleado**: Número de empleado del cliente
- **Empresa**: Empresa a la que pertenece el cliente
- **Unidad**: Unidad organizacional del cliente
- **Optometrista**: Profesional que realizó el examen

### Información de la Orden de Laboratorio
- **Tipo de orden**: Armazón o Lente de contacto (formateado)
- **Esfera OD**: Graduación esfera ojo derecho
- **Esfera OI**: Graduación esfera ojo izquierdo
- **Cilindro OD**: Graduación cilindro ojo derecho
- **Cilindro OI**: Graduación cilindro ojo izquierdo
- **Eje OD**: Eje de graduación ojo derecho
- **Eje OI**: Eje de graduación ojo izquierdo
- **DIP**: Distancia interpupilar
- **AO**: Altura óptica
- **ACO**: Altura de centrado óptico

### Datos de Graduación Subjetiva Final
- **Subjetiva final Esfera OD/OI**: Graduación final determinada
- **Subjetiva final Cilindro OD/OI**: Cilindro final
- **Subjetiva final Eje OD/OI**: Eje final
- **Subjetiva final AV lejos OD/OI**: Agudeza visual de lejos
- **Subjetiva final AV cerca s/Add OD/OI**: Agudeza visual de cerca sin adición
- **Subjetiva final AV cerca c/Add OD/OI**: Agudeza visual de cerca con adición

### Información Específica de Lentes de Contacto
- **Tipo de lente de contacto**: Blando o Rígido (formateado)
- **Curva base**: Curvatura base del lente
- **Diámetro**: Diámetro del lente

### Información Adicional
- **Armazón propio del cliente**: Si el cliente proporciona su propio armazón
- **Observaciones**: Notas adicionales sobre la orden
- **Diseño**: Tipo de diseño del lente
- **Material**: Material del lente
- **Tratamiento**: Tratamiento aplicado al lente
- **Sugerencias**: Sugerencias del optometrista

### Productos Relacionados
- **Productos de la venta**: Lista de productos vendidos (obtenidos desde stockventaordenlaboratorio)
- **Productos de la orden**: Productos específicos de la orden de laboratorio

## Métodos Personalizados Creados

### `getFoliosFromStocks(Ordenlaboratorio $orden): array`
Obtiene los folios de venta relacionados con una orden de laboratorio usando la ruta correcta a través de stockventaordenlaboratorio.

### `getFoliosFromStocksForExport(Ordenlaboratorio $orden): string`
Versión formateada para exportación que devuelve los folios separados por comas.

### `getProductosVentaForExport(Ordenlaboratorio $orden): string`
Obtiene los productos de la venta relacionados con la orden, formateados para exportación.

### `getTipoLenteContactoFormateado(Ordenlaboratorio $orden): string`
Convierte el código numérico del tipo de lente de contacto a texto legible.

### `getTipoOrdenFormateado(Ordenlaboratorio $orden): string`
Convierte el código numérico del tipo de orden a texto legible.

## Consulta SQL Principal Utilizada

```sql
SELECT DISTINCT v.folio 
FROM stockVentaOrdenLaboratorio svol
INNER JOIN stockVenta sv ON svol.stockVenta_idstockVenta = sv.idstockVenta
INNER JOIN venta v ON sv.venta_idventa = v.idventa
WHERE svol.ordenLaboratorio_idordenLaboratorio = ?
AND v.status = '1'
ORDER BY v.folio
```

Esta consulta reemplaza la anterior que usaba `flujoexpedienteventa` y ahora obtiene los folios directamente desde la relación correcta.

## Notas Importantes

1. **Compatibilidad**: Se mantiene un método de respaldo que usa la ruta antigua por compatibilidad.
2. **Logging**: Se agregó logging para debugging en caso de errores.
3. **Validaciones**: Todos los métodos incluyen manejo de errores y valores por defecto.
4. **Formato**: Los campos codificados se formatean automáticamente para mejor legibilidad.
