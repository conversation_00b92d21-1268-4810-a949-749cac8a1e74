# See the configuration reference at https://symfony.com/bundles/SchebTwoFactorBundle/5.x/configuration.html
scheb_two_factor:
    trusted_device:
        enabled: true                 # Enable the trusted device feature
        lifetime: 2592000             # Lifetime of the trusted device token in seconds (30 days)
        extend_lifetime: false        # Do not extend lifetime of the trusted cookie on re-login
        cookie_name: trusted_device   # Name of the trusted device cookie
        cookie_secure: false          # Do not set the 'Secure' flag on the trusted device cookie (false for localhost)
        cookie_same_site: "lax"       # The same-site option of the cookie
        cookie_domain: null           # Use the request domain (null for localhost)
        cookie_path: "/"

    google:
        enabled: true
        server_name: PV360
        issuer: Grupo Optimo
        digits: 6
        window: 1
        template: security/2fa_form.html.twig


    security_tokens:
        - Symfony\Component\Security\Guard\Token\PostAuthenticationGuardToken

    