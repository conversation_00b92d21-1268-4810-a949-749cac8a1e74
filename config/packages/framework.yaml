# see https://symfony.com/doc/current/reference/configuration/framework.html
framework:
    templating:
        engine: 'twig'
    secret: '%env(APP_SECRET)%'
    #csrf_protection: true
    #http_method_override: true

    # Enables session support. Note that the session will ONLY be started if you read or write from it.
    # Remove or comment this section to explicitly disable session support.
    session:
        handler_id: session.handler.native_file
        save_path: '%kernel.project_dir%/var/sessions/%kernel.environment%'
        # 2 días de vida (172800 seconds)
        cookie_lifetime: 172800
        cookie_secure: auto
        cookie_samesite: lax

    #esi: true
    #fragments: true
    php_errors:
        log: true
    assets:
        packages:
            inventario:
                base_path: /uploads/carga-masiva/inventario/Carga-Inv/