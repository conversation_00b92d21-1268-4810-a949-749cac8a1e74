index:
    path: /
    prefix: /login
    controller: App\Controller\AdminLoginController::loginAction

_sonata_admin:
    resource: .
    type: sonata_admin
    prefix: /admin

2fa_login:
    path: /2fa
    defaults:
        _controller: "scheb_two_factor.form_controller::form"

2fa_login_check:
    path: /2fa_check

descargar_archivo:
    path: /descargar/{filename}
    controller: App\Controller\TuControlador::downloadAction

cliente_api_login_check:
    path: /cliente-api/login_check
    controller: lexik_jwt_authentication.controller.login
    methods: [ POST ]

cliente_login:
    path: /cliente/login
    controller: App\Controller\SecurityController::login

cliente_logout:
    path: /cliente/logout
