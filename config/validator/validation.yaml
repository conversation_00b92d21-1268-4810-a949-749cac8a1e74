# config/validator/validation.yaml
App\Entity\Ordenlaboratorio:
    properties:
        disenolenteIddisenolente:
            - Expression:
                expression: "not ((this.getEtapa() > 1 or this.getArmazoncliente() == 1) and this.getDisenolenteIddisenolente() == null and this.getTipoorden() == '1')"
                message: "Ingresa un diseño de lente válido"

        materialIdmaterial:
            - Expression:
                expression: "not ((this.getEtapa() > 1 or this.getArmazoncliente() == 1) and this.getMaterialIdmaterial() == null and this.getTipoorden() == '1')"
                message: "Ingresa un material válido"

        armazoncliente:
            - Expression:
                expression: "not (this.getTipoorden() == '1' and this.getArmazoncliente() == null)"
                message: "Ingresa un valor válido"

        dip:
            - Expression:
                expression: "not ((this.getEtapa() > 1 or this.getArmazoncliente() == 1) and this.getDip() == null and this.getTipoorden() == '1')"
                message: "Ingresa un DIP válido"

        ao:
            - Expression:
                expression: "not ((this.getEtapa() > 1 or this.getArmazoncliente() == 1) and this.getAo() == null and this.getTipoorden() == '1')"
                message: "Ingresa un AO válido"
        
        aco:
            - Expression:
                expression: "not ((this.getEtapa() > 1 or this.getArmazoncliente() == 1) and this.getAco() == null and this.getTipoorden() == '1')"
                message: "Ingresa un ACO válido"
        
        cb:
            - Expression:
                expression: "not (this.getTipoorden() == '2' and this.getCb() == null and this.getEtapa() > 1)"
                message: "Ingresa una curva base válida"

        diam:
            - Expression:
                expression: "not (this.getTipoorden() == '2' and this.getDiam() == null and this.getEtapa() > 1 )"
                message: "Ingresa un diametro válido"

        tipolentecontacto:
            - Expression:
                expression: "not (this.getTipoorden() == '2' and this.getTipolentecontacto() == null and this.getEtapa() > 1)"
                message: "Ingresa un tipo de lente de contacto válido"

App\Entity\Usuario:
    constraints:
        - Symfony\Bridge\Doctrine\Validator\Constraints\UniqueEntity:
            fields: email
            message: 'Ya hay una cuenta registrada con este correo electrónico'

App\Entity\Graduacion:
    properties:
        cb:
            - Expression:
                expression: "not (this.getTipolente() == '2' and this.getCb() == null )"
                message: "Ingresa una curva base válida"

        diam:
            - Expression:
                expression: "not (this.getTipolente() == '2' and this.getDiam() == null )"
                message: "Ingresa un diametro válido"

        tipolentecontacto:
            - Expression:
                expression: "not (this.getTipolente() == '2' and this.getTipolentecontacto() == null )"
                message: "Ingresa un tipo de lente de contacto válido"
        
        materialIdmaterial:
            - Expression:
                expression: "not (this.getTipolente() == '1' and this.getMaterialIdmaterial() == null )"
                message: "Ingresa un material válido"

        disenolenteIddisenolente:
            - Expression:
                expression: "not (this.getTipolente() == '1' and this.getDisenolenteIddisenolente() == null )"
                message: "Ingresa un diseño válido"

        tratamientoIdtratamiento:
            - Expression:
                expression: "not (this.getTipolente() == '1' and this.getTratamientoIdtratamiento() == null )"
                message: "Ingresa un tratamiento válido"