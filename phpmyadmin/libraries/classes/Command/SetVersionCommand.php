<?php

declare(strict_types=1);

namespace PhpMyAdmin\Command;

use RangeException;
use Symfony\Component\Console\Command\Command;
use Symfony\Component\Console\Input\InputArgument;
use Symfony\Component\Console\Input\InputInterface;
use Symfony\Component\Console\Output\OutputInterface;

use function file_put_contents;
use function preg_match;
use function sprintf;

final class SetVersionCommand extends Command
{
    /** @var string */
    protected static $defaultName = 'set-version';

    /** @var string */
    private static $generatedClassTemplate = <<<'PHP'
<?php

declare(strict_types=1);

namespace PhpMyAdmin;

use const VERSION_SUFFIX;

/**
 * This class is generated by scripts/console.
 *
 * @see \PhpMyAdmin\Command\SetVersionCommand
 */
final class Version
{
    // The VERSION_SUFFIX constant is defined at libraries/constants.php
    public const VERSION = '%1$u.%2$u.%3$u%4$s' . VERSION_SUFFIX;
    public const SERIES = '%1$u.%2$u';
    public const MAJOR = %1$u;
    public const MINOR = %2$u;
    public const PATCH = %3$u;
    public const ID = %1$u%2$02u%3$02u;
    public const PRE_RELEASE_NAME = '%5$s';
    public const IS_DEV = %6$s;
}

PHP;

    protected function configure(): void
    {
        $this->setDescription('Sets the version number');
        $this->setHelp('This command generates the PhpMyAdmin\Version class based on the version number provided.');
        $this->addArgument('version', InputArgument::REQUIRED, 'The version number');
    }

    protected function execute(InputInterface $input, OutputInterface $output): int
    {
        /** @var string $version */
        $version = $input->getArgument('version');

        $generatedClass = $this->getGeneratedClass($version);

        if (! $this->writeGeneratedClassFile($generatedClass)) {
            return Command::FAILURE;
        }

        $output->writeln('PhpMyAdmin\Version class successfully generated!');

        return Command::SUCCESS;
    }

    private function getGeneratedClass(string $version): string
    {
        // Do not allow any major below 5
        $return = preg_match('/^([5-9]+)\.(\d{1,2})\.(\d{1,2})(-([a-z0-9]+))?$/', $version, $matches);
        if ($return === false || $return === 0) {
            throw new RangeException('The version number is in the wrong format: ' . $version);
        }

        return sprintf(
            self::$generatedClassTemplate,
            $matches[1],
            $matches[2],
            $matches[3],
            $matches[4] ?? '',
            $matches[5] ?? '',
            ($matches[5] ?? '') === 'dev' ? 'true' : 'false'
        );
    }

    private function writeGeneratedClassFile(string $generatedClass): bool
    {
        $result = file_put_contents(ROOT_PATH . 'libraries/classes/Version.php', $generatedClass);

        return $result !== false;
    }
}
