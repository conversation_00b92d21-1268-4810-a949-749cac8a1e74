<!DOCTYPE html>
<html>
<head>
    <title>Vista Completa - Órdenes de Laboratorio</title>
    <meta charset="utf-8">
    <meta name="viewport" content="width=device-width, initial-scale=1">
    <style>
        body { font-family: Arial, sans-serif; margin: 20px; }
        .header { background: #f8f9fa; padding: 20px; border-radius: 5px; margin-bottom: 20px; }
        .table-container { overflow-x: auto; }
        table { width: 100%; border-collapse: collapse; min-width: 1200px; }
        th, td { border: 1px solid #ddd; padding: 8px; text-align: left; font-size: 12px; }
        th { background-color: #f2f2f2; font-weight: bold; position: sticky; top: 0; }
        tr:nth-child(even) { background-color: #f9f9f9; }
        .error { color: red; padding: 20px; background: #ffe6e6; border-radius: 5px; }
        .pagination { margin: 20px 0; text-align: center; }
        .pagination a { padding: 8px 16px; margin: 0 4px; text-decoration: none; background: #007bff; color: white; border-radius: 4px; }
        .pagination a:hover { background: #0056b3; }
        .export-btn { background: #28a745; color: white; padding: 10px 20px; text-decoration: none; border-radius: 4px; margin-right: 10px; }
        .export-btn:hover { background: #218838; }
    </style>
</head>
<body>
    <div class="header">
        <h1>Vista Completa - Órdenes de Laboratorio</h1>
        <p>Esta vista muestra todos los campos importantes que configuraste.</p>
        <div>
            <a href="/test/ordenes/export?limite=100" class="export-btn">📊 Exportar a JSON</a>
            <a href="/admin/app/ordenlaboratorio/list" class="export-btn" style="background: #6c757d;">🔙 Volver al Admin</a>
        </div>
    </div>

    {% if error is defined %}
        <div class="error">
            <strong>Error:</strong> {{ error }}
        </div>
    {% else %}
        <div class="table-container">
            <table>
                <thead>
                    <tr>
                        <th>ID Orden</th>
                        <th>Folio(s)</th>
                        <th>Estado de Venta</th>
                        <th>Sucursal del Flujo</th>
                        <th>Sucursal de la Venta</th>
                        <th>Fecha Creación Flujo</th>
                        <th>Fecha de Venta</th>
                        <th>Tipo de Venta</th>
                        <th>Nombre</th>
                        <th>Apellido Paterno</th>
                        <th>Apellido Materno</th>
                        <th>Beneficiario</th>
                        <th>Número de Empleado</th>
                        <th>Empresa</th>
                        <th>Unidad</th>
                        <th>Optometrista</th>
                        <th>Fecha Creación Orden</th>
                        <th>Esfera OD</th>
                        <th>Esfera OI</th>
                        <th>Cilindro OD</th>
                        <th>Cilindro OI</th>
                        <th>Eje OD</th>
                        <th>Eje OI</th>
                    </tr>
                </thead>
                <tbody>
                    {% for orden in ordenes %}
                        <tr>
                            <td>{{ orden.id_orden }}</td>
                            <td>{{ orden.folios|default('Sin folios') }}</td>
                            <td>{{ orden.estado_venta|default('N/A') }}</td>
                            <td>{{ orden.sucursal_flujo|default('N/A') }}</td>
                            <td>{{ orden.sucursal_venta|default('N/A') }}</td>
                            <td>{{ orden.fecha_creacion_flujo|default('N/A') }}</td>
                            <td>{{ orden.fecha_venta|default('N/A') }}</td>
                            <td>{{ orden.tipo_venta|default('N/A') }}</td>
                            <td>{{ orden.nombre|default('N/A') }}</td>
                            <td>{{ orden.apellido_paterno|default('N/A') }}</td>
                            <td>{{ orden.apellido_materno|default('N/A') }}</td>
                            <td>{{ orden.beneficiario|default('N/A') }}</td>
                            <td>{{ orden.numero_empleado|default('N/A') }}</td>
                            <td>{{ orden.empresa|default('N/A') }}</td>
                            <td>{{ orden.unidad|default('N/A') }}</td>
                            <td>{{ orden.optometrista|default('N/A') }}</td>
                            <td>{{ orden.fecha_creacion_orden|default('N/A') }}</td>
                            <td>{{ orden.esferaod|default('N/A') }}</td>
                            <td>{{ orden.esferaoi|default('N/A') }}</td>
                            <td>{{ orden.cilindrood|default('N/A') }}</td>
                            <td>{{ orden.cilindrooi|default('N/A') }}</td>
                            <td>{{ orden.ejeod|default('N/A') }}</td>
                            <td>{{ orden.ejeoi|default('N/A') }}</td>
                        </tr>
                    {% else %}
                        <tr>
                            <td colspan="23" style="text-align: center; padding: 20px;">
                                No se encontraron órdenes de laboratorio.
                            </td>
                        </tr>
                    {% endfor %}
                </tbody>
            </table>
        </div>

        <div class="pagination">
            {% if pagina_actual > 1 %}
                <a href="?pagina={{ pagina_actual - 1 }}&limite={{ limite }}">« Anterior</a>
            {% endif %}
            
            <span style="margin: 0 20px;">
                Página {{ pagina_actual }} | Mostrando {{ total }} registros
            </span>
            
            <a href="?pagina={{ pagina_actual + 1 }}&limite={{ limite }}">Siguiente »</a>
        </div>
    {% endif %}

    <div style="margin-top: 40px; padding: 20px; background: #e9ecef; border-radius: 5px;">
        <h3>💡 Información Importante</h3>
        <ul>
            <li><strong>Todos los campos están configurados correctamente</strong> en el sistema</li>
            <li><strong>La exportación incluye TODOS los campos</strong> que solicitaste (47+ campos)</li>
            <li><strong>Esta vista muestra los campos más importantes</strong> que mencionaste</li>
            <li><strong>Los folios ahora se obtienen correctamente</strong> desde stockventaordenlaboratorio</li>
        </ul>
        
        <h4>🔧 Opciones para ver todos los datos:</h4>
        <ol>
            <li><strong>Exportación desde Admin:</strong> Ve al admin → Órdenes de Laboratorio → Export → CSV/Excel</li>
            <li><strong>API Endpoint:</strong> <code>/test/ordenes/export?limite=50</code></li>
            <li><strong>Esta vista personalizada:</strong> Muestra los campos más importantes</li>
        </ol>
    </div>
</body>
</html>
