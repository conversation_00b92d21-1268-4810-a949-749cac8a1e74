<?php
// Script de prueba para verificar la consulta de folios
// Este script debe ejecutarse desde la raíz del proyecto Symfony

require_once 'vendor/autoload.php';

use Symfony\Component\Dotenv\Dotenv;
use Doctrine\DBAL\DriverManager;

// Cargar variables de entorno
$dotenv = new Dotenv();
$dotenv->load('.env');

// Configuración de la base de datos
$connectionParams = [
    'dbname' => $_ENV['DATABASE_NAME'] ?? 'pv360',
    'user' => $_ENV['DATABASE_USER'] ?? 'root',
    'password' => $_ENV['DATABASE_PASSWORD'] ?? '',
    'host' => $_ENV['DATABASE_HOST'] ?? 'localhost',
    'driver' => 'pdo_mysql',
];

try {
    $conn = DriverManager::getConnection($connectionParams);
    
    // Consulta para obtener algunas órdenes de laboratorio de ejemplo
    $ordenes = $conn->fetchAllAssociative("
        SELECT ol.idordenlaboratorio 
        FROM ordenlaboratorio ol 
        WHERE ol.status = '1' 
        LIMIT 5
    ");
    
    echo "=== PRUEBA DE CONSULTA DE FOLIOS ===\n\n";
    
    foreach ($ordenes as $orden) {
        $idOrden = $orden['idordenlaboratorio'];
        echo "Orden de Laboratorio ID: $idOrden\n";
        
        // Consulta nueva (a través de stockventaordenlaboratorio)
        $foliosNueva = $conn->fetchAllAssociative("
            SELECT DISTINCT v.folio 
            FROM stockVentaOrdenLaboratorio svol
            INNER JOIN stockVenta sv ON svol.stockVenta_idstockVenta = sv.idstockVenta
            INNER JOIN venta v ON sv.venta_idventa = v.idventa
            WHERE svol.ordenLaboratorio_idordenLaboratorio = ? 
            AND v.status = '1'
            ORDER BY v.folio
        ", [$idOrden]);
        
        // Consulta antigua (a través de flujoexpedienteventa)
        $foliosAntigua = $conn->fetchAllAssociative("
            SELECT DISTINCT v.folio 
            FROM venta v 
            INNER JOIN flujoexpediente fe ON v.idventa = fe.venta_idventa 
            INNER JOIN ordenlaboratorio ol ON fe.idflujoexpediente = ol.flujoexpediente_idflujoexpediente 
            WHERE ol.idordenlaboratorio = ?
            AND v.status = '1'
        ", [$idOrden]);
        
        echo "  Folios (nueva consulta): " . implode(', ', array_column($foliosNueva, 'folio')) . "\n";
        echo "  Folios (consulta antigua): " . implode(', ', array_column($foliosAntigua, 'folio')) . "\n";
        echo "  Coinciden: " . (array_column($foliosNueva, 'folio') === array_column($foliosAntigua, 'folio') ? 'SÍ' : 'NO') . "\n";
        echo "\n";
    }
    
} catch (Exception $e) {
    echo "Error: " . $e->getMessage() . "\n";
}
