//Eliminar temporales
docker system df
docker system prune -af

//detener todos los contenedores
docker stop $(docker ps -a -q)

//para crear la imagfen

 docker build -t cotizador .
//para correr la imagen
docker run -it -p 9000:9000 cotizador

//para entrar a la consola
docker exec -i -t fe1af4cb649a /bin/bash


iniciar la imagen y contenedor

docker-compose up --build

//dsetener todos los repositorios
docker stop $(docker ps -a -q)


para el error "Error response from daemon: cannot stop container"
sudo aa-remove-unknown


//para quitar lo del error de  gruup by
entrar al contenedor de mysql
docker exec -i -t containerID bash

entrar a mysql
mysql -u root -p
contraseña: root //revisar si no se ha cambiado

    SET PERSIST sql_mode=(SELECT REPLACE(@@sql_mode,'ONLY_FULL_GROUP_BY',''));