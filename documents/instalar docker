A-install docker
1)https://docs.docker.com/engine/install/ubuntu/
2)sudo snap install docker
3)sudo apt  install docker-compose 

##para instalar xdebug
https://www.youtube.com/watch?v=HlBlVCvBXOg

B-Got permission denied issue

1)Create the docker group if it does not exist
$ sudo groupadd docker
2)Add your user to the docker group.
$ sudo usermod -aG docker $USER
3)Log in to the new docker group (to avoid having to log out / log in again; but if not enough, try to reboot):
$ newgrp docker
4)Check if docker can be run without root
$ docker run hello-world
5)Reboot if still got error
$ reboot


C-install repo
//in directory
1)docker-compose up --build
3)import database with workbech
2)go to localhost:8002/