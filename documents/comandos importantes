//comando para deshabilitar el puerto cuando se queda ocupado
fuser -n tcp -k 8000



/* para cambiar los permisos de los archivos en el directorio actual
sudo find . -type f -exec chmod -R 644 {} \;

/* para cambiar los persmiso de las carpetas del directorio actual
sudo find . -type d -exec chmod -R 755 {} \;



Si queremos abreviar el cambio de nuestra versión de php-cli de nuestro Ubuntu podemos ejecutar el siguiente comando:

 sudo update-alternatives --config php

Con:

php -v

veríamos que versión estamos utilizando.

Si queremos cambiarlo de Apache:

En lugar de para cambiar de php7.0 a php5.6:

sudo a2dismod php7.0
sudo a2enmod php5.6SET PERSIST sql_mode=(SELECT REPLACE(@@sql_mode,'ONLY_FULL_GROUP_BY',''));

Y luego reinicio de Apache:


sudo update-alternatives --config php
sudo service apache2 restart

rm -rf var/cache/dev/*


php bin/console doctrine:mapping:import App\Entity annotation --path=src/Entity

php bin/console make:entity --regenerate App
rm -rf var/cache/dev/*
////estilos en el admin
php bin/console assets:install