-- MySQL Triggers to update venta fields after payment operations
-- This script creates triggers to automatically update the pagado and deuda fields
-- in the venta table whenever a payment is inserted, updated, or deleted.

DELIMITER //

-- Drop existing procedures and triggers if they exist
DROP PROCEDURE IF EXISTS update_venta_payment_totals//
DROP TRIGGER IF EXISTS update_venta_after_payment_insert//
DROP TRIGGER IF EXISTS update_venta_after_payment_update//
DROP TRIGGER IF EXISTS update_venta_after_payment_delete//

-- Helper procedure to update venta fields based on payment totals
CREATE PROCEDURE update_venta_payment_totals(IN venta_id INT)
BEGIN
    DECLARE suma_pagos DECIMAL(15, 2);
    DECLARE subtotal DECIMAL(15, 2);
    DECLARE porcentaje_iva INT;
    DECLARE monto_iva DECIMAL(15, 2);
    DECLARE total_con_iva DECIMAL(15, 2);
    DECLARE nueva_deuda DECIMAL(15, 2);
    DECLARE es_liquidada TINYINT(1);

    -- Initialize variables with default values
    SET suma_pagos = 0;
    SET subtotal = 0;
    SET porcentaje_iva = 0;
    SET monto_iva = 0;
    SET total_con_iva = 0;
    SET nueva_deuda = 0;
    SET es_liquidada = 0;

    -- Obtener el subtotal (campo total) y porcentaje de IVA de la venta
    SELECT total, porcentajeiva INTO subtotal, porcentaje_iva
    FROM venta
    WHERE idventa = venta_id;

    -- Calcular el IVA y el total con IVA
    SET monto_iva = ROUND(subtotal * (porcentaje_iva / 100), 2);
    SET total_con_iva = subtotal + monto_iva;

    -- Sumar todos los pagos activos de esta venta
    SELECT COALESCE(SUM(monto), 0) INTO suma_pagos
    FROM pago
    WHERE venta_idventa = venta_id
      AND status = '1';

    -- Calcular la nueva deuda
    SET nueva_deuda = total_con_iva - suma_pagos;

    -- Si la deuda es negativa o el total es cero, se corrige
    IF nueva_deuda < 0 OR total_con_iva = 0 THEN
        SET nueva_deuda = 0;
    END IF;

    -- Evaluar si está liquidada o no
    IF nueva_deuda <= 0 THEN
        SET es_liquidada = 1;
    ELSE
        SET es_liquidada = 0;
    END IF;

    -- Actualizar la venta
    UPDATE venta
    SET 
        pagadoTotal = suma_pagos,     -- Suma de todos los pagos
        pagado = total_con_iva,       -- Total con IVA
        iva = monto_iva,              -- Monto del IVA
        deuda = nueva_deuda,          -- Deuda = total_con_iva - suma_pagos
        liquidada = es_liquidada      -- 1 si deuda <= 0, 0 en otro caso
    WHERE idventa = venta_id;
END //

-- Trigger for INSERT operations
CREATE TRIGGER update_venta_after_payment_insert
AFTER INSERT ON pago
FOR EACH ROW
BEGIN
    IF NEW.venta_idventa IS NOT NULL THEN
        CALL update_venta_payment_totals(NEW.venta_idventa);
    END IF;
END //

-- Trigger for UPDATE operations
CREATE TRIGGER update_venta_after_payment_update
AFTER UPDATE ON pago
FOR EACH ROW
BEGIN
    IF OLD.venta_idventa IS NOT NULL THEN
        CALL update_venta_payment_totals(OLD.venta_idventa);
    END IF;
    
    IF NEW.venta_idventa IS NOT NULL 
       AND (OLD.venta_idventa IS NULL OR NEW.venta_idventa != OLD.venta_idventa) THEN
        CALL update_venta_payment_totals(NEW.venta_idventa);
    END IF;
END //

-- Trigger for DELETE operations
CREATE TRIGGER update_venta_after_payment_delete
AFTER DELETE ON pago
FOR EACH ROW
BEGIN
    IF OLD.venta_idventa IS NOT NULL THEN
        CALL update_venta_payment_totals(OLD.venta_idventa);
    END IF;
END //

DELIMITER ;
