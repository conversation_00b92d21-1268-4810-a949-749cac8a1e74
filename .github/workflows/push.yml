name: Publish pv360

on:
  push:
    branches:
      - master

env:
  REGISTRY: ghcr.io
  IMAGE_NAME: antoonio/pv360:latest
  #GHCR_PAT: ****************************************
  GHCR_PAT: ****************************************
  WORK_DIR: /home/<USER>/github/wrkdirs/pv360
  SSH_HOST: ************
  SSH_USER: antoonio


jobs:
  build:
    runs-on: ubuntu-latest

    steps:
      # Step 1: Checkout the repository codea
      - name: Checkout code
        uses: actions/checkout@v3

      # Step 2: Set up Docker Buildx (a modern Docker build tool)
      - name: Set up Docker Buildx
        uses: docker/setup-buildx-action@v2

      # Step 3: Ensure Docker logout (optional cleanup step)
      - name: Ensure Docker Logout
        run: docker logout ghcr.io || true

      # Step 4: Log in to GitHub Container Registry (GHCR) using your PAT stored in Secrets
      - name: Log in to GitHub Container Registry
        run: echo "${{ env.GHCR_PAT }}" | docker login ${{ env.REGISTRY }} -u ${{ github.actor }} --password-stdin

      # Step 5: Check login status
      - name: Check GHCR Login Status
        run: docker login ${{ env.REGISTRY }} --username ${{ github.actor }} --password-stdin <<< ${{ env.GHCR_PAT }}

      # Step 6: Build and push Docker image, specifying Dockerfile location
      - name: Build and push Docker image
        uses: docker/build-push-action@v5
        with:
          context: .
          file: ./.docker/Dockerfile
          push: true
          tags: ${{ env.REGISTRY }}/${{ env.IMAGE_NAME }}

      - name: Install and Configure SSH Keys
        run: |
          mkdir -p ~/.ssh
          echo "${{ secrets.SSH_PRIVATE_KEY }}" > ~/.ssh/id_rsa
          chmod 600 ~/.ssh/id_rsa
          ssh-keyscan -H ${{ env.SSH_HOST }} >> ~/.ssh/known_hosts
          cat ~/.ssh/id_rsa

      - name: Test SSH Connection
        run: |
          ssh -o StrictHostKeyChecking=no ${{ env.SSH_USER }}@${{ env.SSH_HOST }} "echo 'SSH connection successful'"

      # Step 8: Deploy to server via SSH and run Docker container with .env file
      - name: Deploy to server
        run: |
          ssh -vvv ${{ env.SSH_USER }}@${{ env.SSH_HOST }} << 'EOF'
          echo "Starting deployment..."
          docker logout ghcr.io || true
          echo "${{ env.GHCR_PAT }}" | docker login ghcr.io -u ${{ github.actor }} --password-stdin
          docker pull ${{ env.REGISTRY }}/${{ env.IMAGE_NAME }}
          docker stop pv360_app_1 || true
          docker stop pv360_app_4 || true
          docker rm pv360_app_1 || true
          docker rm pv360_app_4 || true
          docker run -d --network database -p 8000:80 --name pv360_app_1 --env-file ${{ env.WORK_DIR }}/.docker/env/docker.env -v ${{ env.WORK_DIR }}/public/uploads:/var/www/html/public/uploads ${{ env.REGISTRY }}/${{ env.IMAGE_NAME }}
          EOF

      # Step 9: Optional SSH key cleanup
      - name: Remove SSH key from authorized keys (optional)
        if: success()
        run: |
          ssh ${{ env.SSH_USER }}@${{ env.SSH_HOST }} "sed -i '/your-ssh-key-identifier/d' ~/.ssh/authorized_keys"